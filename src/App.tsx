
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Route, Routes, useLocation } from "react-router-dom";
import { ThemeProvider } from "./components/ThemeProvider";
import Navbar from "./components/Navbar";
import SimpleFooter from "./components/Footer";
import { ScrollTop } from "./components/scroll-top";
import NewHome from "./sections/NewHome";
import Software from "./sections/Software";
import Hardware from "./sections/Hardware";
import SupportedModels from "./sections/SupportedModels";
import Resellers from "./sections/Resellers";
import PaymentMethods from "./sections/PaymentMethods";
import Contact from "./sections/Contact";
import WhatsNew from "./pages/WhatsNew";
import KnowledgeBase from "./pages/KnowledgeBase";
import HelpCenter from "./pages/HelpCenter";
import FAQ from "./pages/FAQ";
import TermsOfService from "./pages/TermsOfService";
import NotFound from "./pages/NotFound";
import Pricing from "./sections/Pricing";
import { useEffect } from "react";
import { AnimatePresence, motion } from "framer-motion";
import AnimatedBackground from "./components/AnimatedBackground";
import Background3D from "./components/3D/Background3D";
import Section3D from "./components/3D/Section3D";
import { useInView } from "./hooks/useInView";

// إنشاء queryClient خارج المكون لمنع إعادة الإنشاء عند إعادة التصيير
const queryClient = new QueryClient();

// مكون لإعادة التمرير إلى الأعلى عند تغيير الصفحة
const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
};

// تطبيق محسّن مع هيكل بسيط
const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <ThemeProvider defaultTheme="system">
          <TooltipProvider>
            {/* استخدام هيكل flex للصفحة الكاملة */}
            <div className="flex flex-col min-h-screen">
              {/* مكون للتمرير إلى الأعلى عند تغيير الصفحة */}
              <ScrollToTop />
              
              {/* Navbar في الأعلى */}
              <Navbar />
              
              {/* المحتوى الرئيسي مع flex-grow ليأخذ المساحة المتاحة */}
              <main className="flex-grow">
                <Routes>
                  {/* تعريف المسارات هنا */}
                  <Route path="/" element={<NewHome />} />
                  <Route path="/software" element={<Software />} />
                  <Route path="/hardware" element={<Hardware />} />
                  <Route path="/supported-models" element={<SupportedModels />} />
                  <Route path="/resellers" element={<Resellers />} />
                  <Route path="/payment-methods" element={<PaymentMethods />} />
                  <Route path="/contact" element={<Contact />} />
                  <Route path="/pricing" element={<Pricing />} />
                  <Route path="/whats-new" element={<WhatsNew />} />
                  <Route path="/knowledge-base" element={<KnowledgeBase />} />
                  <Route path="/help-center" element={<HelpCenter />} />
                  <Route path="/faq" element={<FAQ />} />
                  <Route path="/terms-of-service" element={<TermsOfService />} />
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </main>
              
              {/* Footer في الأسفل */}
              <SimpleFooter />
              
              {/* مكونات إضافية */}
              <ScrollTop />
              <Toaster />
              <Sonner />
            </div>
          </TooltipProvider>
        </ThemeProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

export default App;
