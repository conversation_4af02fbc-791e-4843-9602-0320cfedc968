
import React, { useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface FloatingElement3DProps {
  children: React.ReactNode;
  speed?: number;
  offset?: number;
  delay?: number;
  className?: string;
  interactive?: boolean;
  rotate?: boolean;
}

const FloatingElement3D: React.FC<FloatingElement3DProps> = ({
  children,
  speed = 1,
  offset = 10,
  delay = 0,
  className,
  interactive = false,
  rotate = false,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (!interactive || !ref.current) return;
    
    const element = ref.current;
    
    const handleMouseMove = (e: MouseEvent) => {
      const { clientX, clientY } = e;
      const { left, top, width, height } = element.getBoundingClientRect();
      
      const centerX = left + width / 2;
      const centerY = top + height / 2;
      
      const deltaX = (clientX - centerX) / (width / 2);
      const deltaY = (clientY - centerY) / (height / 2);
      
      element.style.transform = `translate3d(${deltaX * 10}px, ${deltaY * 10}px, 0) ${rotate ? `rotateX(${-deltaY * 10}deg) rotateY(${deltaX * 10}deg)` : ''}`;
    };
    
    const handleMouseLeave = () => {
      element.style.transform = 'translate3d(0, 0, 0) rotateX(0) rotateY(0)';
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('mouseleave', handleMouseLeave);
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [interactive, rotate]);
  
  return (
    <motion.div
      ref={ref}
      className={cn(
        'will-change-transform',
        interactive && 'transition-transform duration-200',
        className
      )}
      animate={{
        y: interactive ? 0 : [0, -offset, 0],
        rotate: rotate ? [0, 1, 0, -1, 0] : 0,
      }}
      transition={{
        duration: 5 / speed,
        repeat: interactive ? 0 : Infinity,
        ease: "easeInOut",
        delay,
      }}
    >
      {children}
    </motion.div>
  );
};

export default FloatingElement3D;
