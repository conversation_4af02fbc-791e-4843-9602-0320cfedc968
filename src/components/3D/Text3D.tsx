
import React from 'react';
import { cn } from '@/lib/utils';

interface Text3DProps {
  children: React.ReactNode;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
  depth?: 'none' | 'shallow' | 'medium' | 'deep';
  color?: string;
  shadowColor?: string;
  className?: string;
  textGradient?: boolean;
}

const Text3D: React.FC<Text3DProps> = ({
  children,
  as = 'p',
  size = 'md',
  depth = 'medium',
  color = 'text-pegasus-orange',
  shadowColor = 'text-pegasus-orange-700',
  className,
  textGradient = false,
}) => {
  const Component = as;
  
  const sizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl',
    '3xl': 'text-3xl',
    '4xl': 'text-4xl',
  };
  
  const depthClasses = {
    none: 'text-shadow-none',
    shallow: '[text-shadow:1px_1px_0_var(--shadow),2px_2px_0_var(--shadow)]',
    medium: '[text-shadow:1px_1px_0_var(--shadow),2px_2px_0_var(--shadow),3px_3px_0_var(--shadow)]',
    deep: '[text-shadow:1px_1px_0_var(--shadow),2px_2px_0_var(--shadow),3px_3px_0_var(--shadow),4px_4px_0_var(--shadow)]',
  };

  return (
    <Component
      className={cn(
        'font-bold tracking-tight',
        sizeClasses[size],
        depthClasses[depth],
        textGradient ? 'bg-gradient-to-r from-pegasus-orange via-pegasus-orange-500 to-pegasus-orange-600 bg-clip-text text-transparent' : color,
        className
      )}
      style={{
        '--shadow': `var(--${shadowColor.replace('text-', '')})`,
      } as any}
    >
      {children}
    </Component>
  );
};

export default Text3D;
