
import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useAnimationUtils } from '@/hooks/useAnimationUtils';

const LoadingAnimation: React.FC = () => {
  const [animationComplete, setAnimationComplete] = useState(false);
  const { deferAnimation } = useAnimationUtils();

  // Colors
  const outlineColor = "#FFFFFF";
  const orangeColor = "#F97316";

  // Enhanced animation variants for SVG drawing effect with improved timing
  const pathVariants = {
    hidden: {
      pathLength: 0,
      opacity: 0,
      fill: "rgba(249, 115, 22, 0)"
    },
    visible: (i: number) => ({
      pathLength: 1,
      opacity: 1,
      fill: "rgba(249, 115, 22, 1)",
      transition: {
        pathLength: {
          type: "spring",
          duration: 2.5,
          bounce: 0.3,
          delay: i * 0.25
        },
        opacity: {
          duration: 0.8,
          delay: i * 0.2
        },
        fill: {
          duration: 1.2,
          delay: 2.2 + i * 0.25
        }
      }
    })
  };

  // Text animation variants for more creative text display
  const textVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      filter: "blur(10px)"
    },
    visible: {
      opacity: 1,
      y: 0,
      filter: "blur(0px)",
      transition: {
        duration: 1.2,
        delay: 1.5,
        ease: [0.43, 0.13, 0.23, 0.96]
      }
    }
  };

  // Letter animation for text effect
  const letterVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: 1.8 + i * 0.1,
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1]
      }
    })
  };

  // Enhanced particle system with more variety and better performance
  const particles = Array.from({ length: 30 }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    size: Math.random() * 5 + 1,
    duration: Math.random() * 4 + 2,
    delay: Math.random() * 3,
    opacity: Math.random() * 0.7 + 0.3,
    speed: Math.random() * 0.5 + 0.5
  }));

  // Path data for "Pegasus Tool" logo
  const pegasusToolPath = "m360.6 212.1h-25.7v20.9h-20.1v-20.8l19.2-17.7h23.5q4.1 0 6.5-2.4 2.6-2.5 2.6-6.6 0-4.1-2.6-6.3-2.4-2.2-6.5-2.2h-28l-14.7-17.6h45.8q5.9 0 10.7 1.9 4.8 1.9 8.2 5.4 3.5 3.5 5.3 8.3 1.9 4.8 1.9 10.7 0 6-1.9 10.9-1.8 4.9-5.3 8.3-3.4 3.4-8.2 5.3-4.8 1.9-10.7 1.9zm35.6-52.7h62.3v17.6h-42.1v9.3h42.1l-18.8 17.6h-23.2v11.6h42v17.5h-62.3zm110.9 75.7q-8.8 0-16.3-2.8-7.5-2.8-13.1-8-5.5-5.1-8.7-12.3-3.1-7.1-3.1-15.9 0-8.7 3.1-15.9 3.2-7.1 8.7-12.2 5.6-5.1 13.2-7.9 7.5-2.8 16.3-2.8 8.5 0 16.1 2.6 7.8 2.7 13 7.6l-14.1 12.8q-2.5-2.6-6-4-3.5-1.5-7.9-1.5-4.5 0-8.5 1.6-4 1.5-7 4.3-3.1 2.9-4.9 6.9-1.7 3.9-1.7 8.6 0 4.8 1.7 8.8 1.8 3.9 4.9 6.7 3 2.8 7 4.3 4.1 1.5 8.6 1.5 1.2 0 2.8-0.1 1.5-0.1 3.1-0.3 1.6-0.2 3-0.4 1.4-0.3 2.4-0.8v-8.6l-15.4-17.6h35.7v43.3h-14.4q-1.6 0.5-4 0.9-2.5 0.3-5.1 0.6-2.5 0.2-5 0.5-2.5 0.1-4.4 0.1zm42.3-2.1l10.7-38.5h31.1l-9.8-35.1h20.9l20.7 73.6h-21l-5.9-21h-19.9l-5.9 21zm116-45.9q8.2 0 13.9 2 5.7 1.9 9.1 5.2 3.6 3.2 5.2 7.3 1.7 4.1 1.7 8.6 0 4.5-1.8 8.9-1.7 4.4-5.6 8-3.9 3.5-10.2 5.8-6.3 2.2-15.3 2.2-4.4 0-8.5-0.9-4.1-0.9-7.6-2.4-3.5-1.5-6.3-3.3-2.7-1.7-4.7-3.6l14.1-12.9q2.1 2.5 5.4 4 3.4 1.5 8.3 1.5 5.7 0 8.7-1.8 3.2-1.9 3.2-4.1 0-2.4-3.6-4.4-3.5-2-11.2-2-8.3 0-14-1.9-5.7-2-9.2-5.1-3.5-3.3-5.1-7.4-1.6-4.2-1.6-8.5 0-4.5 1.7-8.9 1.8-4.5 5.7-8.1 3.9-3.6 10.2-5.8 6.3-2.2 15.3-2.2 4.4 0 8.5 0.9 4.1 0.9 7.6 2.4 3.5 1.4 6.2 3.2 2.8 1.8 4.8 3.7l-14 12.8q-2.1-2.4-5.5-3.9-3.4-1.6-8.3-1.6-5.7 0-8.8 1.9-3.1 1.8-3.1 4 0 2.4 3.6 4.4 3.6 2 11.2 2zm91.5-8.9l20.1-18.8v40.2q0 8.9-3.1 15.5-3.1 6.6-8.2 11.1-5.1 4.4-11.6 6.7-6.4 2.2-13.2 2.2-6.7 0-13.3-2.2-6.5-2.3-11.6-6.7-5.1-4.5-8.2-11.1-3.1-6.6-3.1-15.5v-40.2h20.2v38.9q0 4.8 1.4 8.4 1.3 3.6 3.6 6 2.3 2.4 5.1 3.7 2.9 1.1 5.9 1.1 3 0 5.9-1.1 2.8-1.3 5-3.7 2.3-2.4 3.7-6 1.4-3.6 1.4-8.4zm64.7 8.9q8.2 0 13.9 2 5.6 1.9 9.1 5.2 3.6 3.2 5.2 7.3 1.6 4.1 1.6 8.5 0 4.5-1.8 9-1.6 4.4-5.5 7.9-3.9 3.6-10.2 5.8-6.3 2.2-15.4 2.2-4.4 0-8.5-0.8-4.1-1-7.5-2.4-3.5-1.5-6.3-3.3-2.8-1.8-4.8-3.7l14.1-12.8q2.1 2.4 5.5 4 3.3 1.5 8.3 1.5 5.7 0 8.7-1.8 3.2-1.9 3.2-4.1 0-2.4-3.6-4.4-3.6-2-11.3-2-8.3 0-13.9-1.9-5.7-2-9.3-5.2-3.5-3.2-5-7.3-1.6-4.2-1.6-8.5 0-4.5 1.7-9 1.8-4.5 5.6-8 3.9-3.6 10.2-5.8 6.3-2.2 15.4-2.2 4.4 0 8.5 0.9 4.1 0.9 7.6 2.3 3.4 1.5 6.2 3.3 2.8 1.8 4.8 3.7l-14.1 12.8q-2.1-2.4-5.4-3.9-3.4-1.6-8.4-1.6-5.6 0-8.8 1.9-3 1.8-3 4 0 2.4 3.5 4.4 3.6 2 11.3 2zm-508.9 80.8v-17.5h72.3v17.5h-25.6v37.4l-20.1 18.6v-56zm76.2 19.1q0-8.7 2.8-15.8 2.8-7.2 7.9-12.2 5.1-5.2 12.3-7.9 7.1-2.8 15.8-2.8 8.8 0 16 2.8 7.1 2.7 12.3 7.9 5.1 5 7.9 12.2 2.8 7.1 2.8 15.8 0 8.9-2.8 16-2.8 7.2-7.9 12.3-5.2 5.2-12.3 8-7.2 2.7-16 2.7-8.7 0-15.8-2.7-7.2-2.8-12.3-8-5.1-5.1-7.9-12.3-2.8-7.1-2.8-16zm20.3 0.2q0 9.6 4.7 15.5 4.7 5.8 13.7 5.8 4.6 0 8.1-1.5 3.5-1.6 5.9-4.3 2.4-2.8 3.6-6.7 1.2-4 1.2-8.8 0-4.8-1.2-8.7-1.2-4-3.6-6.8-2.4-2.8-5.9-4.3-3.5-1.6-8.1-1.6-9 0-13.7 5.9-4.7 5.9-4.7 15.5zm64.9-0.2q0-8.7 2.7-15.8 2.8-7.2 7.9-12.2 5.1-5.2 12.3-7.9 7.1-2.8 15.8-2.8 8.9 0 16 2.8 7.2 2.7 12.3 7.9 5.2 5 7.9 12.2 2.8 7.1 2.8 15.8 0 8.9-2.8 16-2.7 7.2-7.9 12.3-5.1 5.2-12.3 8-7.1 2.7-16 2.7-8.7 0-15.8-2.7-7.2-2.8-12.3-8-5.1-5.1-7.9-12.3-2.7-7.1-2.7-16zm20.2 0.2q0 9.6 4.7 15.5 4.7 5.8 13.7 5.8 4.7 0 8.1-1.5 3.5-1.6 5.9-4.3 2.4-2.8 3.6-6.7 1.2-4 1.2-8.8 0-4.8-1.2-8.7-1.2-4-3.6-6.8-2.4-2.8-5.9-4.3-3.4-1.6-8.1-1.6-9 0-13.7 5.9-4.7 5.9-4.7 15.5zm65-18l20.3-18.8v56h42v17.5h-62.3z";

  // End animation after 5.5 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      // بدء تأثير الدائرة قبل إكمال الرسوم المتحركة
      startCircularReveal();
      
      // ثم تعيين حالة اكتمال الرسوم المتحركة بعد فترة قصيرة
      setTimeout(() => {
        setAnimationComplete(true);
      }, 1000); // تأخير قصير بعد بدء تأثير الدائرة
    }, 4500); // تقليل وقت الانتظار قبل بدء التأثير

    return () => clearTimeout(timer);
  }, []);

  // دالة منفصلة لبدء تأثير الكشف الدائري
  const startCircularReveal = () => {
    // Get the mask overlay element
    const maskOverlay = document.getElementById('mask-overlay');
    if (!maskOverlay) {
      // إنشاء عنصر mask-overlay إذا لم يكن موجودًا
      const newMaskOverlay = document.createElement('div');
      newMaskOverlay.id = 'mask-overlay';
      document.body.appendChild(newMaskOverlay);
      
      // استخدام العنصر الجديد
      createCircularReveal(newMaskOverlay);
    } else {
      // استخدام العنصر الموجود
      createCircularReveal(maskOverlay);
    }
  };

  // دالة منفصلة لإنشاء تأثير الكشف الدائري
  const createCircularReveal = (maskOverlay: HTMLElement) => {
    // إزالة أي عناصر ripple متبقية من محاولات سابقة
    document.querySelectorAll('.loading-ripple, .loading-small-ripple').forEach(el => {
      el.remove();
    });
    
    // Set initial state for the mask with improved positioning
    maskOverlay.style.position = 'fixed';
    maskOverlay.style.top = '0';
    maskOverlay.style.left = '0';
    maskOverlay.style.width = '100%';
    maskOverlay.style.height = '100%';
    maskOverlay.style.clipPath = 'circle(0% at center)';
    maskOverlay.style.opacity = '1';
    maskOverlay.style.background = 'radial-gradient(circle, rgba(249, 115, 22, 0.7) 0%, rgba(0, 0, 0, 0.9) 70%)';
    maskOverlay.style.zIndex = '9999';
    maskOverlay.style.pointerEvents = 'none'; // تأكد من أن العنصر لا يمنع التفاعل مع العناصر الأخرى
    
    // Create initial small pulse before the main ripple - تنفيذ فوري
    for (let i = 0; i < 3; i++) {
      setTimeout(() => {
        const smallRipple = document.createElement('div');
        smallRipple.className = 'loading-small-ripple';
        smallRipple.style.position = 'fixed'; // استخدام fixed بدلاً من absolute
        smallRipple.style.top = '50%';
        smallRipple.style.left = '50%';
        smallRipple.style.transform = 'translate(-50%, -50%)';
        smallRipple.style.width = '5px';
        smallRipple.style.height = '5px';
        smallRipple.style.borderRadius = '50%';
        smallRipple.style.backgroundColor = '#F97316';
        smallRipple.style.boxShadow = '0 0 10px 2px rgba(249, 115, 22, 0.7)';
        smallRipple.style.opacity = '1';
        smallRipple.style.zIndex = '10000';
        smallRipple.style.pointerEvents = 'none'; // تأكد من أن العنصر لا يمنع التفاعل مع العناصر الأخرى
        document.body.appendChild(smallRipple);
        
        const animation = smallRipple.animate(
          [
            { transform: 'translate(-50%, -50%) scale(1)', opacity: 1 },
            { transform: 'translate(-50%, -50%) scale(20)', opacity: 0 }
          ],
          { 
            duration: 600, 
            easing: 'ease-out'
          }
        );
        
        animation.onfinish = () => {
          if (smallRipple.parentNode) {
            smallRipple.parentNode.removeChild(smallRipple);
          }
        };
      }, i * 200);
    }
    
    // Trigger the main ripple effect after the small pulses
    setTimeout(() => {
      const ripple = document.createElement('div');
      ripple.className = 'loading-ripple';
      ripple.style.position = 'fixed'; // استخدام fixed بدلاً من absolute
      ripple.style.top = '50%';
      ripple.style.left = '50%';
      ripple.style.transform = 'translate(-50%, -50%)';
      ripple.style.width = '10px';
      ripple.style.height = '10px';
      ripple.style.borderRadius = '50%';
      ripple.style.backgroundColor = '#F97316';
      ripple.style.boxShadow = '0 0 20px 5px rgba(249, 115, 22, 0.7)';
      ripple.style.opacity = '1';
      ripple.style.zIndex = '10000';
      ripple.style.pointerEvents = 'none'; // تأكد من أن العنصر لا يمنع التفاعل مع العناصر الأخرى
      document.body.appendChild(ripple);
      
      // Animate the ripple
      const animation = ripple.animate(
        [
          { transform: 'translate(-50%, -50%) scale(1)', opacity: 1 },
          { transform: 'translate(-50%, -50%) scale(100)', opacity: 0 }
        ],
        { 
          duration: 1500, 
          easing: 'cubic-bezier(0.22, 1, 0.36, 1)'
        }
      );
      
      animation.onfinish = () => {
        if (ripple.parentNode) {
          ripple.parentNode.removeChild(ripple);
        }
        
        // Animate the mask to reveal the content with smoother transition
        const maskAnimation = maskOverlay.animate(
          [
            { clipPath: 'circle(0% at center)', opacity: 1 },
            { clipPath: 'circle(75% at center)', opacity: 0.8 },
            { clipPath: 'circle(150% at center)', opacity: 0 }
          ],
          { 
            duration: 1400, 
            easing: 'cubic-bezier(0.65, 0, 0.35, 1)', 
            fill: 'forwards' as FillMode
          }
        );
        
        maskAnimation.onfinish = () => {
          // إزالة أي عناصر متبقية بعد اكتمال التأثير
          document.querySelectorAll('.loading-ripple, .loading-small-ripple').forEach(el => {
            if (el.parentNode) {
              el.parentNode.removeChild(el);
            }
          });
        };
      };
    }, 600);
  };

  return (
    <motion.div
      initial={{ opacity: 1 }}
      animate={animationComplete ? { opacity: 0 } : { opacity: 1 }}
      transition={{ duration: 0.5, delay: 1.5 }} // تأخير إضافي لإتاحة الوقت لتأثير الدائرة
      onAnimationComplete={() => {
        if (animationComplete) {
          // إزالة الفئة 'loading' وإضافة الفئة 'loaded'
          document.body.classList.remove('loading');
          document.body.classList.add('loaded');

          // إزالة عنصر loading-root بعد اكتمال تأثير الدائرة
          const loadingRoot = document.getElementById('loading-root');
          if (loadingRoot && loadingRoot.parentNode) {
            setTimeout(() => {
              if (loadingRoot.parentNode) {
                loadingRoot.parentNode.removeChild(loadingRoot);
              }
            }, 1000); // تقليل وقت الانتظار بعد اكتمال التأثير
          }
        }
      }}
      className="fixed inset-0 flex flex-col items-center justify-center bg-gray-900 z-50"
    >
      {/* Enhanced background particle effects with better performance */}
      <div className="absolute inset-0 pointer-events-none">
        {particles.map(particle => (
          <motion.div
            key={particle.id}
            className="absolute rounded-full bg-orange-500"
            initial={{
              x: `${particle.x}%`,
              y: `${particle.y}%`,
              opacity: 0,
              scale: 0
            }}
            animate={{
              opacity: [0, particle.opacity, 0],
              scale: [0, 1, 0],
              x: `${particle.x + (Math.random() * 30 - 15)}%`,
              y: `${particle.y + (Math.random() * 30 - 15)}%`
            }}
            transition={{
              duration: particle.duration,
              delay: particle.delay,
              repeat: Infinity,
              repeatDelay: Math.random() * 2,
              ease: "easeInOut"
            }}
            style={{
              width: particle.size,
              height: particle.size
            }}
          />
        ))}
      </div>

      {/* Enhanced glow behind the logo with pulsating effect */}
      <motion.div
        className="absolute w-80 h-80 bg-orange-500 rounded-full filter blur-[120px] opacity-20"
        animate={{
          scale: [1, 1.4, 1],
          opacity: [0.1, 0.3, 0.1]
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      {/* Secondary glow for depth effect */}
      <motion.div
        className="absolute w-60 h-60 bg-orange-400 rounded-full filter blur-[80px] opacity-15"
        animate={{
          scale: [1.2, 0.9, 1.2],
          opacity: [0.15, 0.25, 0.15]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 0.5
        }}
      />

      {/* Logo container with improved animations */}
      <div className="w-full max-w-md relative">
        {/* SVG container with 3D perspective effect */}
        <motion.div
          initial={{ rotateY: -10, rotateX: 10 }}
          animate={{ 
            rotateY: [0, 5, 0, -5, 0],
            rotateX: [0, -5, 0, 5, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          style={{ perspective: 1000 }}
          className="w-full"
        >
          <motion.svg
            viewBox="0 0 930 465"
            className="w-full drop-shadow-2xl"
            initial="hidden"
            animate="visible"
          >
            {/* Subtle background glow for the logo */}
            <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur stdDeviation="15" result="blur" />
              <feComposite in="SourceGraphic" in2="blur" operator="over" />
            </filter>
            
            {/* Triangle shape (decorative element) with enhanced animation */}
            <motion.path
              d={pegasusToolPath}
              stroke={outlineColor}
              strokeWidth="3"
              strokeLinecap="round"
              strokeLinejoin="round"
              fill="transparent"
              custom={0}
              variants={pathVariants}
              filter="url(#glow)"
            />

            {/* Pegasus Tool SVG Path with enhanced animation */}
            <motion.path
              d="m166.7 114q3.7 0 6.7 2.1 47.6 27.2 94.6 55.4 0.8 39.6 0 79.2-30.3 18.6-61.3 36.3-3.8 2.7-7.1-0.4-0.4-37.5-0.8-75.1-16.5-10.3-33.4-20-0.4 79.2-0.8 158.4-1.4 2.1-3.7 2.5-30.6-17.4-60.9-35.4-3.1-1.5-4.6-4.6-0.8-77.5 0-155 1.7-4.6 6.3-3 29.1 17.1 58.3 34.2 2.4 1.6 5 1.7-1.4-36.4-0.4-73 0.8-1.8 2.1-3.3z"
              stroke={outlineColor}
              strokeWidth="3"
              strokeLinecap="round"
              strokeLinejoin="round"
              fill="transparent"
              custom={1}
              variants={pathVariants}
              filter="url(#glow)"
            />
          </motion.svg>
        </motion.div>

        {/* Additional animated elements around the logo */}
        <div className="absolute inset-0">
          {/* Multiple animated particles for more dynamic effect */}
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={`particle-${i}`}
              className={`absolute w-${i+1} h-${i+1} rounded-full bg-orange-${400 + i * 50}`}
              initial={{ x: `-${20 * i}%`, y: `-${10 * i}%`, opacity: 0 }}
              animate={{
                x: [`-${10 * i}%`, `${110 - i * 10}%`],
                y: [`${10 * i}%`, `${90 - i * 10}%`],
                opacity: [0, 0.7, 0]
              }}
              transition={{
                duration: 3 + i * 0.5,
                delay: i * 0.2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </div>

      {/* Enhanced loading progress bar with animation */}
      <div className="absolute bottom-10 left-0 right-0 text-center">
        <motion.div
          className="relative w-64 h-6 mx-auto"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 0.5 }}
        >
          <div className="w-full h-1.5 bg-gray-800 rounded-full overflow-hidden">
            <motion.div
              initial={{ width: "0%" }}
              animate={{ width: "100%" }}
              transition={{ 
                duration: 5, 
                ease: [0.34, 1.56, 0.64, 1] // Custom spring-like easing
              }}
              className="h-full bg-gradient-to-r from-orange-400 via-orange-500 to-orange-600 rounded-full"
            />
          </div>
          <motion.p
            className="text-white mt-2 font-bold tracking-wider"
            initial={{ opacity: 0 }}
            animate={{ 
              opacity: [0, 1, 0.7, 1],
              y: [5, 0]
            }}
            transition={{
              opacity: { duration: 2, repeat: 2, repeatType: "reverse" },
              y: { duration: 0.5 }
            }}
          >
            Loading...
          </motion.p>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default LoadingAnimation;
