
import React from 'react';
import { useNavigate, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Drawer, DrawerClose, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import { X, Menu } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface MenuItem {
  title: string;
  href: string;
}

interface MobileNavProps {
  menuItems: MenuItem[];
}

const MobileNav: React.FC<MobileNavProps> = ({ menuItems }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [open, setOpen] = React.useState(false);

  // Modified to handle section navigation and page navigation properly
  const handleNavigation = (href: string) => {
    setOpen(false); // Close the drawer first
    
    // Use setTimeout to ensure drawer is closed before navigation
    setTimeout(() => {
      if (href.startsWith('#')) {
        // Try to find the element on the current page first
        const element = document.getElementById(href.substring(1));
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
          return;
        }
        
        // If not found or not on home page, navigate to the home page first
        if (location.pathname !== '/') {
          navigate('/', { state: { scrollTo: href.substring(1) } }); 
        }
      } else {
        // For non-hash links, use regular navigation
        navigate(href);
      }
    }, 300); // Give time for the drawer animation to complete
  };

  // Animation variants for menu items
  const menuItemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: (i: number) => ({
      opacity: 1, 
      x: 0,
      transition: {
        delay: i * 0.1 + 0.3,
        duration: 0.5,
        ease: [0.22, 1, 0.36, 1]
      }
    }),
    exit: { 
      opacity: 0, 
      x: 20,
      transition: {
        duration: 0.3
      }
    }
  };

  return (
    <div className="md:hidden">
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerTrigger asChild>
          <Button 
            variant="ghost" 
            size="icon"
            className={cn(
              "ml-2 transition-all duration-300",
              open ? "bg-gradient-to-r from-pegasus-orange to-green-500 text-white hover:bg-green-600" : ""
            )}
            aria-label="Toggle menu"
          >
            <Menu className="h-6 w-6" />
          </Button>
        </DrawerTrigger>
        <DrawerContent className="h-[80vh] p-0">
          <div className="container mx-auto p-4">
            <div className="flex justify-end mb-4">
              <DrawerClose asChild>
                <Button variant="ghost" size="icon">
                  <X className="h-6 w-6" />
                </Button>
              </DrawerClose>
            </div>
            <div className="flex flex-col space-y-4">
              <AnimatePresence mode="wait">
                {open && menuItems.map((item, index) => (
                  <motion.button
                    key={item.title}
                    onClick={() => handleNavigation(item.href)}
                    className={cn(
                      "py-3 font-medium transition-all duration-200 border-b border-gray-100 dark:border-gray-800 text-left",
                      // Default styling
                      "text-gray-700 dark:text-gray-200 hover:text-pegasus-orange",
                      // Active route styling
                      (location.pathname === item.href || 
                       (location.pathname === '/' && item.href === '/')) && 
                      "text-pegasus-orange dark:text-green-500 font-semibold"
                    )}
                    custom={index}
                    variants={menuItemVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    whileTap={{ scale: 0.97 }}
                  >
                    {item.title}
                  </motion.button>
                ))}
              </AnimatePresence>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </div>
  );
};

export default MobileNav;
