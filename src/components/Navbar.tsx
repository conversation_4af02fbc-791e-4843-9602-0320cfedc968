
import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ThemeToggle } from "@/components/ThemeToggle";
import { motion } from "framer-motion";
import { supabase } from "@/integrations/supabase/client";
import MobileNav from "./MobileNav";

interface MenuItem {
  title: string;
  href: string;
}

interface LatestUpdate {
  varizon: string;
  link: string | null;
}

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [latestUpdate, setLatestUpdate] = useState<LatestUpdate | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  
  // Updated menu items
  const menuItems: MenuItem[] = [
    { title: "Home", href: "/" },
    { title: "Software", href: "/software" },
    { title: "Hardware", href: "/hardware" },
    { title: "Resellers", href: "/resellers" },
    { title: "Pricing", href: "/pricing" },
    { title: "What's New", href: "/whats-new" },
    { title: "Contact", href: "/contact" },
  ];

  // Handle scrolling effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Fetch latest version
  useEffect(() => {
    const fetchLatestUpdate = async () => {
      try {
        const { data, error } = await supabase
          .from('update')
          .select('varizon, link')
          .order('release_at', { ascending: false })
          .limit(1);
        
        if (error) throw error;
        if (data && data.length > 0) {
          setLatestUpdate(data[0]);
        }
      } catch (error) {
        console.error('Error fetching latest update:', error);
      }
    };

    fetchLatestUpdate();
  }, []);

  // Modified to handle section navigation and page navigation properly
  const scrollToSection = (href: string) => {
    if (href.startsWith('#')) {
      // Always try to scroll to section on current page first
      const element = document.getElementById(href.substring(1));
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
        return;
      }
      
      // If we're not on the home page and section wasn't found, navigate home with scrollTo
      if (location.pathname !== '/') {
        navigate('/', { state: { scrollTo: href.substring(1) } }); 
      }
    } else {
      // For non-hash links, use regular navigation
      navigate(href);
    }
  };

  return (
    <motion.header 
      className={cn(
        "fixed w-full z-50 top-0 transition-all duration-500",
        isScrolled 
          ? "bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm shadow-md py-3" 
          : "bg-transparent py-5"
      )}
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link to="/" className="flex items-center group">
            <motion.span 
              className={cn(
                "font-bold text-2xl transition-all duration-300",
                isScrolled 
                  ? "bg-gradient-to-r from-pegasus-orange to-green-500 bg-clip-text text-transparent" 
                  : "bg-gradient-to-r from-pegasus-orange to-green-400 bg-clip-text text-transparent"
              )}
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              Pegasus Tool
              <span className="block h-0.5 w-0 group-hover:w-full transition-all duration-300 bg-gradient-to-r from-pegasus-orange to-green-500"></span>
            </motion.span>
          </Link>
          
          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {menuItems.map((item) => (
              <motion.a
                key={item.title}
                href={item.href}
                onClick={(e) => {
                  e.preventDefault();
                  scrollToSection(item.href);
                }}
                className={cn(
                  "font-medium transition-all duration-200 relative py-2 px-1",
                  "after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5 after:bg-gradient-to-r after:from-pegasus-orange after:to-green-500 after:scale-x-0 after:origin-right after:transition-transform after:duration-300",
                  "hover:text-pegasus-orange hover:after:scale-x-100 hover:after:origin-left",
                  isScrolled ? "text-gray-700 dark:text-gray-200" : "text-gray-800 dark:text-gray-100",
                  // Highlight active route
                  (location.pathname === item.href || 
                   (location.pathname === '/' && item.href === '/')) && 
                  "text-pegasus-orange dark:text-pegasus-orange"
                )}
                whileHover={{ y: -2 }}
                whileTap={{ scale: 0.97 }}
              >
                {item.title}
              </motion.a>
            ))}
          </nav>
          
          {/* Mobile Menu Button */}
          <div className="flex items-center">
            <ThemeToggle />
            <MobileNav menuItems={menuItems} />
          </div>
        </div>
      </div>
    </motion.header>
  );
}

export default Navbar;
