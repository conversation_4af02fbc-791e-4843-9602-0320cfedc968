
import React from "react";
import { <PERSON>, <PERSON> } from "lucide-react";
import { useTheme } from "./ThemeProvider";
import { But<PERSON> } from "./ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "./ui/dropdown-menu";

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon" 
          className="rounded-full w-10 h-10 flex items-center justify-center hover:bg-gradient-to-r hover:from-pegasus-orange/10 hover:to-green-500/10 transition-all duration-300"
        >
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0 text-pegasus-orange" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100 text-green-500" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="border border-gray-200 dark:border-gray-700 shadow-lg backdrop-blur-sm bg-white/90 dark:bg-gray-900/90">
        <DropdownMenuItem 
          onClick={() => setTheme("light")} 
          className="hover:bg-gradient-to-r hover:from-pegasus-orange/10 hover:to-pegasus-orange/5 cursor-pointer flex items-center gap-2 transition-all"
        >
          <Sun className="h-4 w-4 text-pegasus-orange" />
          <span className={theme === "light" ? "font-medium text-pegasus-orange" : ""}>
            Light
          </span>
          {theme === "light" && (
            <span className="ml-auto h-1.5 w-1.5 rounded-full bg-pegasus-orange"></span>
          )}
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => setTheme("dark")} 
          className="hover:bg-gradient-to-r hover:from-green-500/10 hover:to-green-500/5 cursor-pointer flex items-center gap-2 transition-all"
        >
          <Moon className="h-4 w-4 text-green-500" />
          <span className={theme === "dark" ? "font-medium text-green-500" : ""}>
            Dark
          </span>
          {theme === "dark" && (
            <span className="ml-auto h-1.5 w-1.5 rounded-full bg-green-500"></span>
          )}
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => setTheme("system")} 
          className="hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer flex items-center gap-2 transition-all"
        >
          <div className="h-4 w-4 flex items-center justify-center">🖥️</div>
          <span className={theme === "system" ? "font-medium" : ""}>
            System
          </span>
          {theme === "system" && (
            <span className="ml-auto h-1.5 w-1.5 rounded-full bg-gray-400"></span>
          )}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
