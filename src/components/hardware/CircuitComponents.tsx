
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { use3DEffect } from '@/hooks/use3DEffect';
import SectionHeader from '@/components/SectionHeader';
import Text3D from '@/components/3D/Text3D';
import { HardDrive, MemoryStick, Cpu } from 'lucide-react';

interface ComponentData {
  name: string;
  description: string;
  image: string;
  function: string;
}

const CircuitComponents = () => {
  const componentRef = use3DEffect({
    intensity: 15,
    perspective: 1000,
    glare: true
  });

  // Component data
  const components: ComponentData[] = [
    {
      name: 'Main Processor',
      description: 'The central processing unit that controls all device operations.',
      image: '/lovable-uploads/processor.png',
      function: 'Executes program instructions and manages device operations.'
    },
    {
      name: 'Flash Memory Chip',
      description: 'Non-volatile storage chip that retains data even when powered off.',
      image: '/lovable-uploads/memory-chip.png',
      function: 'Stores firmware, operating system, and critical device data.'
    },
    {
      name: 'USB Controller',
      description: 'Manages USB communications between the device and connected phones.',
      image: '/lovable-uploads/usb-controller.png',
      function: 'Handles data transfer protocols and connection management.'
    }
  ];

  const [activeComponent, setActiveComponent] = useState(0);

  const currentComponent = components[activeComponent];

  return (
    <section id="circuit-components" className="py-20 bg-gradient-to-br from-white/95 via-green-50/90 to-green-100/85 dark:from-gray-800/98 dark:via-gray-900/95 dark:to-green-900/40 overflow-hidden relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-[0.03]">
        <div className="absolute inset-0 bg-[url('/patterns/dots.svg')]"></div>
      </div>

      {/* Animated Gradient Overlay */}
      <div className="absolute inset-0 opacity-100 transition-opacity duration-1000 bg-gradient-to-br from-green-400/8 via-green-300/5 to-green-600/12"></div>

      {/* Subtle Texture Overlay */}
      <div className="absolute inset-0 opacity-[0.02] bg-gradient-to-br from-white via-transparent to-gray-100 dark:from-gray-700 dark:via-transparent dark:to-gray-800"></div>

      <div className="container mx-auto px-4 relative z-10">
        <SectionHeader
          title="Circuit Components"
          subtitle="Explore the key components used in our hardware design"
          highlightWord="Components"
        />

        <div className="flex flex-col lg:flex-row items-center gap-10 mt-16">
          {/* Left side - Interactive Component Viewer */}
          <motion.div
            className="lg:w-1/2"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <div className="relative">
              <Card
                ref={componentRef}
                className="p-8 border-green-200 dark:border-green-900 bg-gradient-to-br from-green-50 to-white dark:from-gray-900 dark:to-gray-800 shadow-lg overflow-hidden transform-gpu preserve-3d"
              >
                <div className="flex justify-center mb-6">
                  <div className="relative w-64 h-64 bg-black/5 rounded-lg p-4 flex items-center justify-center">
                    <img
                      src={currentComponent.image}
                      alt={currentComponent.name}
                      className="max-w-full max-h-full object-contain"
                    />

                    {/* Glowing effect points */}
                    <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <div className="absolute top-1/2 right-1/4 w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <div className="absolute bottom-1/4 left-1/3 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                </div>

                <div className="text-center mb-6">
                  <Text3D
                    as="h3"
                    size="xl"
                    color="text-green-600"
                    className="mb-2"
                  >
                    {currentComponent.name}
                  </Text3D>
                </div>

                <div className="flex justify-center mt-4 gap-2">
                  {components.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setActiveComponent(index)}
                      className={`w-3 h-3 rounded-full transition-colors ${
                        index === activeComponent ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-700'
                      }`}
                      aria-label={`View component ${index + 1}`}
                    />
                  ))}
                </div>
              </Card>
            </div>
          </motion.div>

          {/* Right side - Component Description */}
          <motion.div
            className="lg:w-1/2"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <div className="space-y-6">
              <Text3D
                as="h2"
                size="3xl"
                color="text-green-600"
                className="mb-6"
              >
                Key Hardware Components
              </Text3D>

              <p className="text-lg text-gray-700 dark:text-gray-300">
                {currentComponent.description}
              </p>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-100 dark:border-gray-700">
                <h4 className="font-bold text-lg mb-2 text-green-600 dark:text-green-500">
                  Function:
                </h4>
                <p className="text-gray-700 dark:text-gray-300">
                  {currentComponent.function}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
                <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-100 dark:border-gray-700 text-center">
                  <HardDrive className="h-8 w-8 mx-auto mb-2 text-green-500" />
                  <h5 className="font-medium">Storage</h5>
                </div>

                <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-100 dark:border-gray-700 text-center">
                  <MemoryStick className="h-8 w-8 mx-auto mb-2 text-green-500" />
                  <h5 className="font-medium">Memory</h5>
                </div>

                <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-100 dark:border-gray-700 text-center">
                  <Cpu className="h-8 w-8 mx-auto mb-2 text-green-500" />
                  <h5 className="font-medium">Processing</h5>
                </div>
              </div>

              <Button
                className="mt-6 bg-green-500 hover:bg-green-600 text-white"
                onClick={() => document.getElementById('hardware-supported-models')?.scrollIntoView({ behavior: 'smooth' })}
              >
                View Supported Models
              </Button>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Bottom Accent Line */}
      <motion.div
        className="absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r from-transparent via-green-400 to-transparent"
        initial={{ scaleX: 0 }}
        whileInView={{ scaleX: 1 }}
        transition={{ duration: 1.5, delay: 0.5 }}
        viewport={{ once: true }}
      />
    </section>
  );
};

export default CircuitComponents;
