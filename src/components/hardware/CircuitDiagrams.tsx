
import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { use3DEffect } from '@/hooks/use3DEffect';
import { cn } from '@/lib/utils';
import SectionHeader from '@/components/SectionHeader';
import Text3D from '@/components/3D/Text3D';
import { CircuitBoard, Microchip, Cpu } from 'lucide-react';

const CircuitDiagrams = () => {
  const diagramRef = use3DEffect({
    intensity: 15,
    perspective: 1000,
    glare: true
  });

  const [activeCircuit, setActiveCircuit] = useState<'main' | 'power' | 'io'>('main');
  const [animatePath, setAnimatePath] = useState(false);

  useEffect(() => {
    // Start animation after component mount
    setTimeout(() => {
      setAnimatePath(true);
    }, 500);
  }, []);

  const circuitImages = {
    main: '/lovable-uploads/main-circuit.png',
    power: '/lovable-uploads/power-circuit.png',
    io: '/lovable-uploads/io-circuit.png'
  };

  return (
    <section id="circuit-diagrams" className="py-20 bg-gradient-to-br from-green-100/90 via-green-50/95 to-white/90 dark:from-gray-800/95 dark:via-gray-900/90 dark:to-green-900/25 overflow-hidden relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-[0.03]">
        <div className="absolute inset-0 bg-[url('/patterns/dots.svg')]"></div>
      </div>

      {/* Animated Gradient Overlay */}
      <div className="absolute inset-0 opacity-100 transition-opacity duration-1000 bg-gradient-to-br from-green-400/8 via-green-300/5 to-green-600/12"></div>

      {/* Subtle Texture Overlay */}
      <div className="absolute inset-0 opacity-[0.02] bg-gradient-to-br from-white via-transparent to-gray-100 dark:from-gray-700 dark:via-transparent dark:to-gray-800"></div>

      <div className="container mx-auto px-4 relative z-10">
        <SectionHeader
          title="Circuit Diagrams"
          subtitle="Explore detailed schematics for device diagnostics"
          highlightWord="Circuit"
        />

        <div className="flex flex-col lg:flex-row items-center gap-10 mt-16">
          {/* Right side - Interactive Circuit */}
          <motion.div
            className="lg:w-1/2 order-1 lg:order-2"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <div className="relative">
              <Card
                ref={diagramRef}
                className="p-6 border-green-200 dark:border-green-900 bg-gradient-to-br from-green-50 to-white dark:from-gray-900 dark:to-gray-800 shadow-lg overflow-hidden transform-gpu preserve-3d"
              >
                <div className="relative">
                  <img
                    src={circuitImages[activeCircuit]}
                    alt="Circuit Diagram"
                    className="w-full rounded-md"
                  />

                  {/* Circuit paths that glow - generic for all circuit types */}
                  <svg
                    className="absolute inset-0 w-full h-full"
                    viewBox="0 0 400 300"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    {/* Path 1 */}
                    <path
                      d="M50,50 C100,50 100,100 150,100 L250,100"
                      fill="none"
                      stroke="rgba(34, 197, 94, 0.8)"
                      strokeWidth="1.5"
                      strokeDasharray="200"
                      strokeDashoffset={animatePath ? "0" : "200"}
                      style={{
                        transition: "stroke-dashoffset 2s ease-in-out",
                        filter: "drop-shadow(0 0 3px rgba(34, 197, 94, 0.8))"
                      }}
                    />

                    {/* Path 2 */}
                    <path
                      d="M250,100 C300,100 300,150 350,150"
                      fill="none"
                      stroke="rgba(34, 197, 94, 0.8)"
                      strokeWidth="1.5"
                      strokeDasharray="120"
                      strokeDashoffset={animatePath ? "0" : "120"}
                      style={{
                        transition: "stroke-dashoffset 2s ease-in-out 0.5s",
                        filter: "drop-shadow(0 0 3px rgba(34, 197, 94, 0.8))"
                      }}
                    />

                    {/* Path 3 */}
                    <path
                      d="M150,100 C150,150 200,150 200,200 L200,250"
                      fill="none"
                      stroke="rgba(34, 197, 94, 0.8)"
                      strokeWidth="1.5"
                      strokeDasharray="180"
                      strokeDashoffset={animatePath ? "0" : "180"}
                      style={{
                        transition: "stroke-dashoffset 2s ease-in-out 1s",
                        filter: "drop-shadow(0 0 3px rgba(34, 197, 94, 0.8))"
                      }}
                    />

                    {/* Connection points */}
                    <circle cx="150" cy="100" r="3" fill="#22c55e" className="animate-pulse" />
                    <circle cx="250" cy="100" r="3" fill="#22c55e" className="animate-pulse" />
                    <circle cx="200" cy="200" r="3" fill="#22c55e" className="animate-pulse" />
                  </svg>
                </div>

                <div className="flex justify-center mt-6 gap-4">
                  <button
                    onClick={() => setActiveCircuit('main')}
                    className={cn(
                      "p-2 px-4 rounded-md transition-all",
                      activeCircuit === 'main'
                        ? "bg-green-500 text-white"
                        : "bg-gray-100 dark:bg-gray-800 hover:bg-green-100 dark:hover:bg-green-900/30"
                    )}
                  >
                    Main Board
                  </button>
                  <button
                    onClick={() => setActiveCircuit('power')}
                    className={cn(
                      "p-2 px-4 rounded-md transition-all",
                      activeCircuit === 'power'
                        ? "bg-green-500 text-white"
                        : "bg-gray-100 dark:bg-gray-800 hover:bg-green-100 dark:hover:bg-green-900/30"
                    )}
                  >
                    Power Circuit
                  </button>
                  <button
                    onClick={() => setActiveCircuit('io')}
                    className={cn(
                      "p-2 px-4 rounded-md transition-all",
                      activeCircuit === 'io'
                        ? "bg-green-500 text-white"
                        : "bg-gray-100 dark:bg-gray-800 hover:bg-green-100 dark:hover:bg-green-900/30"
                    )}
                  >
                    I/O Ports
                  </button>
                </div>
              </Card>
            </div>
          </motion.div>

          {/* Left side - Explanation */}
          <motion.div
            className="lg:w-1/2 order-2 lg:order-1"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <Text3D
              as="h2"
              size="3xl"
              color="text-green-600"
              className="mb-6"
            >
              Understanding Circuit Diagrams
            </Text3D>

            <div className="space-y-6 text-gray-700 dark:text-gray-300">
              <p className="text-lg">
                Our interactive circuit diagrams provide detailed schematics for hardware diagnostics
                and repair. Highlighted connection paths help you trace signals and identify components.
              </p>

              <div className="space-y-4 mt-6">
                <div className="flex items-start gap-4">
                  <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-full">
                    <CircuitBoard className="h-6 w-6 text-green-600 dark:text-green-500" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg text-green-600 dark:text-green-500">Main Circuit Board</h3>
                    <p>The central processing board containing the main controller and key components for device operation.</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-full">
                    <Microchip className="h-6 w-6 text-green-600 dark:text-green-500" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg text-green-600 dark:text-green-500">Power Management</h3>
                    <p>Dedicated circuits for power distribution and management, including voltage regulators and protection components.</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-full">
                    <Cpu className="h-6 w-6 text-green-600 dark:text-green-500" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg text-green-600 dark:text-green-500">I/O Connections</h3>
                    <p>Input/output connections, interfaces, and ports for connecting to various peripherals and devices.</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Bottom Accent Line */}
      <motion.div
        className="absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r from-transparent via-green-400 to-transparent"
        initial={{ scaleX: 0 }}
        whileInView={{ scaleX: 1 }}
        transition={{ duration: 1.5, delay: 0.5 }}
        viewport={{ once: true }}
      />
    </section>
  );
};

export default CircuitDiagrams;
