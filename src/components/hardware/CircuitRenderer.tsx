import React, { useEffect, useRef, useState } from 'react';
import { fabric } from 'fabric';
import { Card } from '@/components/ui/card';
import { CircuitBoard, Cpu, Component } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

// Define component types
type ComponentType = 'resistor' | 'capacitor' | 'ic' | 'wire' | 'connection';

interface CircuitComponentPosition {
  x: number;
  y: number;
  rotation?: number;
  label?: string;
}

interface CircuitComponent {
  type: ComponentType;
  position: CircuitComponentPosition;
  id: string;
}

interface ConnectionPath {
  from: string;
  to: string;
  points?: fabric.Point[];
  color: string;
}

interface CircuitRendererProps {
  className?: string;
}

/**
 * Circuit drawing component
 */
const CircuitRenderer: React.FC<CircuitRendererProps> = ({ className }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);
  const [activeCircuit, setActiveCircuit] = useState<'main' | 'power' | 'io'>('main');

  // Initialize canvas on component load
  useEffect(() => {
    if (canvasRef.current && !canvas) {
      const fabricCanvas = new fabric.Canvas(canvasRef.current, {
        width: 800,
        height: 500,
        backgroundColor: '#f8f9fa'
      });
      setCanvas(fabricCanvas);
      
      // Draw background grid
      drawBackgroundGrid(fabricCanvas);
    }
    
    return () => {
      canvas?.dispose();
    };
  }, []);

  // Re-render when active circuit changes
  useEffect(() => {
    if (canvas) {
      renderCircuit(activeCircuit);
    }
  }, [activeCircuit, canvas]);

  // Draw grid background
  const drawBackgroundGrid = (canvas: fabric.Canvas) => {
    const gridSize = 20;
    const width = canvas.width || 800;
    const height = canvas.height || 500;
    
    for (let i = 0; i < width / gridSize; i++) {
      const line = new fabric.Line([i * gridSize, 0, i * gridSize, height], {
        stroke: '#e0e0e0',
        selectable: false
      });
      canvas.add(line);
    }
    
    for (let i = 0; i < height / gridSize; i++) {
      const line = new fabric.Line([0, i * gridSize, width, i * gridSize], {
        stroke: '#e0e0e0',
        selectable: false
      });
      canvas.add(line);
    }
  };
  
  // Create resistor component
  const createResistor = (position: CircuitComponentPosition) => {
    const group = new fabric.Group([], { 
      left: position.x, 
      top: position.y,
      selectable: false 
    });
    
    // Draw resistor body
    const body = new fabric.Rect({ 
      width: 60, 
      height: 20, 
      fill: '#fff',
      stroke: '#000',
      strokeWidth: 1.5,
      originX: 'center',
      originY: 'center'
    });
    
    // Draw wires
    const wireLeft = new fabric.Line([-60, 0, -30, 0], {
      stroke: '#ff0000',
      strokeWidth: 1.5,
      originX: 'center',
      originY: 'center'
    });
    
    const wireRight = new fabric.Line([30, 0, 60, 0], {
      stroke: '#ff0000',
      strokeWidth: 1.5,
      originX: 'center',
      originY: 'center'
    });
    
    // Draw text label
    const text = new fabric.Text(position.label || "R", {
      fontSize: 12,
      fill: '#000',
      originX: 'center',
      originY: 'center',
    });
    
    group.addWithUpdate(body);
    group.addWithUpdate(wireLeft);
    group.addWithUpdate(wireRight);
    group.addWithUpdate(text);
    
    if (position.rotation) {
      group.rotate(position.rotation);
    }
    
    return group;
  };
  
  // Create capacitor component
  const createCapacitor = (position: CircuitComponentPosition) => {
    const group = new fabric.Group([], { 
      left: position.x, 
      top: position.y,
      selectable: false 
    });
    
    // Draw capacitor parts
    const line1 = new fabric.Line([-5, -15, -5, 15], {
      stroke: '#000',
      strokeWidth: 2,
      originX: 'center',
      originY: 'center'
    });
    
    const line2 = new fabric.Line([5, -15, 5, 15], {
      stroke: '#000',
      strokeWidth: 2,
      originX: 'center',
      originY: 'center'
    });
    
    // Draw wires
    const wireLeft = new fabric.Line([-40, 0, -5, 0], {
      stroke: '#ff0000',
      strokeWidth: 1.5,
      originX: 'center',
      originY: 'center'
    });
    
    const wireRight = new fabric.Line([5, 0, 40, 0], {
      stroke: '#ff0000',
      strokeWidth: 1.5,
      originX: 'center',
      originY: 'center'
    });
    
    // Draw text label
    const text = new fabric.Text(position.label || "C", {
      fontSize: 12,
      fill: '#000',
      originX: 'center',
      originY: 'center',
      top: -20
    });
    
    group.addWithUpdate(line1);
    group.addWithUpdate(line2);
    group.addWithUpdate(wireLeft);
    group.addWithUpdate(wireRight);
    group.addWithUpdate(text);
    
    if (position.rotation) {
      group.rotate(position.rotation);
    }
    
    return group;
  };
  
  // Create IC component
  const createIC = (position: CircuitComponentPosition) => {
    const group = new fabric.Group([], { 
      left: position.x, 
      top: position.y,
      selectable: false
    });
    
    // Draw IC body
    const body = new fabric.Rect({ 
      width: 80, 
      height: 120,
      rx: 5,
      ry: 5,
      fill: '#9fc5e8',
      stroke: '#000',
      strokeWidth: 1.5,
      originX: 'center',
      originY: 'center'
    });
    
    // Add pins on sides
    const pinsCount = 8;
    const pinSpacing = 100 / (pinsCount + 1);
    
    // Draw pins on left side
    for(let i = 1; i <= pinsCount/2; i++) {
      const pin = new fabric.Line([-40, -60 + i * pinSpacing * 1.2, -60, -60 + i * pinSpacing * 1.2], {
        stroke: '#ff0000',
        strokeWidth: 1.5,
        originX: 'center',
        originY: 'center'
      });
      group.addWithUpdate(pin);
    }
    
    // Draw pins on right side
    for(let i = 1; i <= pinsCount/2; i++) {
      const pin = new fabric.Line([40, -60 + i * pinSpacing * 1.2, 60, -60 + i * pinSpacing * 1.2], {
        stroke: '#ff0000',
        strokeWidth: 1.5,
        originX: 'center',
        originY: 'center'
      });
      group.addWithUpdate(pin);
    }
    
    // Top marker
    const notch = new fabric.Circle({
      radius: 5,
      fill: '#000',
      top: -55,
      originX: 'center',
      originY: 'center'
    });
    
    // Draw text label
    const text = new fabric.Text(position.label || "IC", {
      fontSize: 16,
      fill: '#000',
      originX: 'center',
      originY: 'center',
    });
    
    group.addWithUpdate(body);
    group.addWithUpdate(notch);
    group.addWithUpdate(text);
    
    if (position.rotation) {
      group.rotate(position.rotation);
    }
    
    return group;
  };
  
  // Create connection point
  const createConnectionPoint = (position: CircuitComponentPosition) => {
    const circle = new fabric.Circle({
      radius: 4,
      fill: '#0000ff',
      left: position.x,
      top: position.y,
      selectable: false,
      originX: 'center',
      originY: 'center'
    });
    
    return circle;
  };
  
  // Draw wire path
  const drawWire = (path: ConnectionPath) => {
    let line;
    
    if (path.points && path.points.length > 1) {
      // Complex multi-point path
      const points = path.points.flatMap(p => [p.x, p.y]);
      line = new fabric.Polyline(points as number[], {
        stroke: path.color || '#ff0000',
        strokeWidth: 1.5,
        fill: '',
        selectable: false
      });
    } else {
      // Straight line between two points
      line = new fabric.Line([200, 200, 400, 400], {
        stroke: path.color || '#ff0000',
        strokeWidth: 1.5,
        selectable: false
      });
    }
    
    return line;
  };

  // Render complete circuit
  const renderCircuit = (circuitType: 'main' | 'power' | 'io') => {
    if (!canvas) return;
    
    // Clear canvas before drawing
    canvas.clear();
    drawBackgroundGrid(canvas);
    
    const components: CircuitComponent[] = [];
    const connections: ConnectionPath[] = [];
    
    // Setup components and connections based on circuit type
    switch (circuitType) {
      case 'main':
        // Main circuit board components and connections
        components.push(
          { type: 'ic', position: { x: 400, y: 250, label: 'CPU' }, id: 'cpu' },
          { type: 'capacitor', position: { x: 250, y: 150, label: 'C1' }, id: 'cap1' },
          { type: 'resistor', position: { x: 550, y: 150, label: 'R1' }, id: 'res1' },
          { type: 'resistor', position: { x: 550, y: 350, label: 'R2', rotation: 90 }, id: 'res2' },
          { type: 'capacitor', position: { x: 250, y: 350, label: 'C2', rotation: 90 }, id: 'cap2' },
          { type: 'connection', position: { x: 250, y: 250 }, id: 'conn1' },
          { type: 'connection', position: { x: 550, y: 250 }, id: 'conn2' }
        );

        connections.push(
          { from: 'cap1', to: 'cpu', color: '#ff0000', 
            points: [
              new fabric.Point(250, 150), 
              new fabric.Point(300, 150), 
              new fabric.Point(300, 200), 
              new fabric.Point(350, 200)
            ] 
          },
          { from: 'res1', to: 'cpu', color: '#ff0000',
            points: [
              new fabric.Point(550, 150),
              new fabric.Point(500, 150),
              new fabric.Point(500, 200),
              new fabric.Point(450, 200)
            ]
          },
          { from: 'cap2', to: 'cpu', color: '#0000ff',
            points: [
              new fabric.Point(250, 350),
              new fabric.Point(300, 350),
              new fabric.Point(300, 300),
              new fabric.Point(350, 300)
            ]
          },
          { from: 'res2', to: 'cpu', color: '#0000ff',
            points: [
              new fabric.Point(550, 350),
              new fabric.Point(500, 350),
              new fabric.Point(500, 300),
              new fabric.Point(450, 300)
            ]
          },
          { from: 'conn1', to: 'cap1', color: '#00ff00',
            points: [
              new fabric.Point(250, 250),
              new fabric.Point(200, 250),
              new fabric.Point(200, 150),
              new fabric.Point(210, 150)
            ]
          },
          { from: 'conn2', to: 'res1', color: '#00ff00',
            points: [
              new fabric.Point(550, 250),
              new fabric.Point(600, 250),
              new fabric.Point(600, 150),
              new fabric.Point(580, 150)
            ]
          }
        );
        break;
        
      case 'power':
        // Power management circuit components and connections
        components.push(
          { type: 'ic', position: { x: 400, y: 250, label: 'PWR' }, id: 'pwr_ic' },
          { type: 'capacitor', position: { x: 250, y: 150, label: 'C10' }, id: 'cap10' },
          { type: 'capacitor', position: { x: 250, y: 350, label: 'C11' }, id: 'cap11' },
          { type: 'resistor', position: { x: 550, y: 200, label: 'R10', rotation: 90 }, id: 'res10' },
          { type: 'resistor', position: { x: 550, y: 300, label: 'R11', rotation: 90 }, id: 'res11' }
        );

        connections.push(
          { from: 'cap10', to: 'pwr_ic', color: '#ff0000',
            points: [
              new fabric.Point(290, 150),
              new fabric.Point(340, 150),
              new fabric.Point(340, 220),
              new fabric.Point(360, 220)
            ]
          },
          { from: 'cap11', to: 'pwr_ic', color: '#0000ff',
            points: [
              new fabric.Point(290, 350),
              new fabric.Point(340, 350),
              new fabric.Point(340, 280),
              new fabric.Point(360, 280)
            ]
          },
          { from: 'res10', to: 'pwr_ic', color: '#ff0000',
            points: [
              new fabric.Point(550, 200),
              new fabric.Point(500, 200),
              new fabric.Point(480, 220)
            ]
          },
          { from: 'res11', to: 'pwr_ic', color: '#0000ff',
            points: [
              new fabric.Point(550, 300),
              new fabric.Point(500, 300),
              new fabric.Point(480, 280)
            ]
          }
        );
        break;
        
      case 'io':
        // Input/output circuit components and connections
        components.push(
          { type: 'ic', position: { x: 400, y: 250, label: 'I/O' }, id: 'io_ic' },
          { type: 'resistor', position: { x: 250, y: 150, label: 'R20' }, id: 'res20' },
          { type: 'resistor', position: { x: 250, y: 350, label: 'R21' }, id: 'res21' },
          { type: 'resistor', position: { x: 550, y: 150, label: 'R22' }, id: 'res22' },
          { type: 'resistor', position: { x: 550, y: 350, label: 'R23' }, id: 'res23' }
        );

        connections.push(
          { from: 'res20', to: 'io_ic', color: '#0000ff',
            points: [
              new fabric.Point(280, 150),
              new fabric.Point(340, 150),
              new fabric.Point(340, 220),
              new fabric.Point(360, 220)
            ]
          },
          { from: 'res21', to: 'io_ic', color: '#0000ff',
            points: [
              new fabric.Point(280, 350),
              new fabric.Point(340, 350),
              new fabric.Point(340, 280),
              new fabric.Point(360, 280)
            ]
          },
          { from: 'res22', to: 'io_ic', color: '#ff0000',
            points: [
              new fabric.Point(520, 150),
              new fabric.Point(460, 150),
              new fabric.Point(460, 220),
              new fabric.Point(440, 220)
            ]
          },
          { from: 'res23', to: 'io_ic', color: '#ff0000',
            points: [
              new fabric.Point(520, 350),
              new fabric.Point(460, 350),
              new fabric.Point(460, 280),
              new fabric.Point(440, 280)
            ]
          }
        );
        break;
    }

    // Draw all wires first to be in the background layer
    connections.forEach(connection => {
      const wire = drawWire(connection);
      canvas.add(wire);
    });
    
    // Draw all components
    components.forEach(component => {
      let fabricComponent;
      
      switch (component.type) {
        case 'resistor':
          fabricComponent = createResistor(component.position);
          break;
        case 'capacitor':
          fabricComponent = createCapacitor(component.position);
          break;
        case 'ic':
          fabricComponent = createIC(component.position);
          break;
        case 'connection':
          fabricComponent = createConnectionPoint(component.position);
          break;
        default:
          return;
      }
      
      canvas.add(fabricComponent);
    });
    
    // Add circuit information
    const title = new fabric.Text(`${
      circuitType === 'main' ? 'Main Processor Circuit Diagram' : 
      circuitType === 'power' ? 'Power Management Circuit Diagram' : 
      'Input/Output Circuit Diagram'
    }`, {
      left: 400,
      top: 30,
      fontSize: 18,
      fontWeight: 'bold',
      fill: '#0d6efd',
      originX: 'center',
      selectable: false
    });
    
    canvas.add(title);
    canvas.renderAll();
  };

  return (
    <div className={cn("circuit-renderer", className)}>
      <Card className="p-6 border-green-200 dark:border-green-900 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 shadow-lg overflow-hidden">
        <div className="mb-6 flex justify-center space-x-4 rtl:space-x-reverse">
          <Button 
            onClick={() => setActiveCircuit('main')}
            className={cn(
              "px-4 py-2 rounded-md transition-all",
              activeCircuit === 'main'
                ? "bg-green-500 text-white"
                : "bg-gray-100 dark:bg-gray-800 hover:bg-green-100 dark:hover:bg-green-900/30"
            )}
          >
            <CircuitBoard className="mr-2 h-5 w-5" />
            Main Processor Circuit
          </Button>
          <Button 
            onClick={() => setActiveCircuit('power')}
            className={cn(
              "px-4 py-2 rounded-md transition-all",
              activeCircuit === 'power'
                ? "bg-green-500 text-white"
                : "bg-gray-100 dark:bg-gray-800 hover:bg-green-100 dark:hover:bg-green-900/30"
            )}
          >
            <Component className="mr-2 h-5 w-5" />
            Power Management Circuit
          </Button>
          <Button 
            onClick={() => setActiveCircuit('io')}
            className={cn(
              "px-4 py-2 rounded-md transition-all",
              activeCircuit === 'io'
                ? "bg-green-500 text-white"
                : "bg-gray-100 dark:bg-gray-800 hover:bg-green-100 dark:hover:bg-green-900/30"
            )}
          >
            <Cpu className="mr-2 h-5 w-5" />
            Input/Output Circuit
          </Button>
        </div>
        
        <div className="relative border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
          <canvas ref={canvasRef} className="w-full h-full block mx-auto" />
        </div>
        
        <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
          <p>* Click the buttons above to view different circuit diagram types</p>
          <p>* Circuit diagram simulated based on standard components and specifications</p>
        </div>
      </Card>
    </div>
  );
};

export default CircuitRenderer;
