
import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Download, ArrowRight, CircuitBoard } from 'lucide-react';
import { use3DEffect } from '@/hooks/use3DEffect';
import Text3D from '@/components/3D/Text3D';
import { fabric } from 'fabric';

const HardwareHero = () => {
  const hardwareImageRef = use3DEffect({
    intensity: 20,
    perspective: 1000,
    glare: true,
    scale: true
  });

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);
  const [latestDiagram, setLatestDiagram] = useState<{
    version: string;
    name: string | null;
    link: string | null;
  } | null>(null);
  const [animatedLines, setAnimatedLines] = useState<fabric.Line[]>([]);
  const [connectionPoints, setConnectionPoints] = useState<fabric.Circle[]>([]);
  const animationRef = useRef<number>();

  // Initialize canvas and draw circuit
  useEffect(() => {
    if (canvasRef.current && !canvas) {
      const fabricCanvas = new fabric.Canvas(canvasRef.current, {
        width: 500,
        height: 400,
        backgroundColor: 'transparent'
      });

      setCanvas(fabricCanvas);
      drawCircuit(fabricCanvas);
      startPowerLineAnimation(fabricCanvas);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      canvas?.dispose();
    };
  }, [canvasRef]);

  // Start power line animation
  const startPowerLineAnimation = (canvas: fabric.Canvas) => {
    const animatePowerFlow = () => {
      // Animate connection points with pulsing effect
      connectionPoints.forEach((point, index) => {
        const time = Date.now() * 0.003 + index * 0.5;
        const scale = 1 + Math.sin(time) * 0.3;
        const opacity = 0.7 + Math.sin(time * 1.5) * 0.3;

        point.set({
          scaleX: scale,
          scaleY: scale,
          opacity: opacity
        });
      });

      // Animate flowing energy along lines
      animatedLines.forEach((line, index) => {
        const time = Date.now() * 0.002 + index * 0.3;
        const intensity = 0.5 + Math.sin(time) * 0.5;
        const glowColor = `rgba(0, 255, 157, ${intensity})`;

        line.set({
          stroke: glowColor,
          strokeWidth: 1.5 + Math.sin(time * 2) * 0.5,
          shadow: new fabric.Shadow({
            color: glowColor,
            blur: 5 + Math.sin(time) * 3,
            offsetX: 0,
            offsetY: 0
          })
        });
      });

      canvas.renderAll();
      animationRef.current = requestAnimationFrame(animatePowerFlow);
    };

    animatePowerFlow();
  };

  // Add interactive hover effects
  const addInteractiveEffects = (canvas: fabric.Canvas) => {
    canvas.on('mouse:over', (e: any) => {
      if (e.target && (animatedLines.includes(e.target as fabric.Line) || connectionPoints.includes(e.target as fabric.Circle))) {
        const target = e.target;
        target.set({
          stroke: '#00ffff',
          fill: '#00ffff',
          shadow: new fabric.Shadow({
            color: '#00ffff',
            blur: 15,
            offsetX: 0,
            offsetY: 0
          })
        });
        canvas.renderAll();
      }
    });

    canvas.on('mouse:out', (e: any) => {
      if (e.target && (animatedLines.includes(e.target as fabric.Line) || connectionPoints.includes(e.target as fabric.Circle))) {
        const target = e.target;
        target.set({
          stroke: '#00ff9d',
          fill: '#00ff9d',
          shadow: null
        });
        canvas.renderAll();
      }
    });
  };

  // Draw the circuit diagram
  const drawCircuit = (canvas: fabric.Canvas) => {
    // Create our circuit lines
    const lines = drawCircuitLines(canvas);
    setAnimatedLines(lines);

    // Create connection points
    const points = drawConnectionPoints(canvas);
    setConnectionPoints(points);

    // Optional ground symbol
    drawGroundSymbol(canvas);

    // Add interactive effects
    addInteractiveEffects(canvas);

    // Make everything non-selectable
    canvas.selection = false;
    canvas.getObjects().forEach((obj: any) => {
      obj.selectable = false;
    });

    canvas.renderAll();
  };

  // Draw circuit lines for the main grid structure
  const drawCircuitLines = (canvas: fabric.Canvas): fabric.Line[] => {
    const circuitColor = '#00ff9d';
    const lineWidth = 1.5;
    const lines: fabric.Line[] = [];

    // Outer border rectangle (we'll skip this for animation as it's a rectangle)
    addRectangle(canvas, 60, 60, 380, 280, circuitColor, lineWidth);

    // Inner grid lines - horizontal
    for (let y = 100; y <= 300; y += 40) {
      const line = addLine(canvas, 80, y, 420, y, circuitColor, lineWidth);
      lines.push(line);
    }

    // Inner grid lines - vertical
    for (let x = 100; x <= 400; x += 40) {
      const line = addLine(canvas, x, 80, x, 320, circuitColor, lineWidth);
      lines.push(line);
    }

    // Additional circuit paths
    lines.push(addLine(canvas, 20, 120, 60, 120, circuitColor, lineWidth));
    lines.push(addLine(canvas, 20, 240, 60, 240, circuitColor, lineWidth));
    lines.push(addLine(canvas, 440, 120, 480, 120, circuitColor, lineWidth));
    lines.push(addLine(canvas, 440, 280, 480, 280, circuitColor, lineWidth));

    // Complex paths - outer borders
    lines.push(addLine(canvas, 100, 40, 400, 40, circuitColor, lineWidth));
    lines.push(addLine(canvas, 100, 40, 100, 60, circuitColor, lineWidth));
    lines.push(addLine(canvas, 400, 40, 400, 60, circuitColor, lineWidth));

    lines.push(addLine(canvas, 80, 360, 420, 360, circuitColor, lineWidth));
    lines.push(addLine(canvas, 80, 320, 80, 360, circuitColor, lineWidth));
    lines.push(addLine(canvas, 420, 320, 420, 360, circuitColor, lineWidth));

    // Diagonal interconnects
    lines.push(addLine(canvas, 140, 100, 180, 140, circuitColor, lineWidth));
    lines.push(addLine(canvas, 220, 180, 260, 220, circuitColor, lineWidth));
    lines.push(addLine(canvas, 300, 140, 340, 100, circuitColor, lineWidth));
    lines.push(addLine(canvas, 340, 260, 380, 220, circuitColor, lineWidth));

    // Circuit loops (rectangles - skip for animation)
    addRectangle(canvas, 140, 180, 80, 80, circuitColor, lineWidth);
    addRectangle(canvas, 280, 180, 80, 80, circuitColor, lineWidth);

    // Add additional circuit element in the middle area
    lines.push(addLine(canvas, 250, 200, 250, 240, circuitColor, lineWidth));

    // Vertical connector to bottom
    lines.push(addLine(canvas, 250, 320, 250, 380, circuitColor, lineWidth));

    // Add a small box at the bottom (rectangle - skip for animation)
    addRectangle(canvas, 220, 380, 60, 40, circuitColor, lineWidth);

    return lines;
  };

  // Draw connection points at line intersections
  const drawConnectionPoints = (canvas: fabric.Canvas): fabric.Circle[] => {
    const pointColor = '#00ff9d';
    const pointRadius = 2;
    const points: fabric.Circle[] = [];

    // Outer connection points
    points.push(addPoint(canvas, 20, 120, pointColor, pointRadius));
    points.push(addPoint(canvas, 20, 240, pointColor, pointRadius));
    points.push(addPoint(canvas, 480, 120, pointColor, pointRadius));
    points.push(addPoint(canvas, 480, 280, pointColor, pointRadius));

    // Grid connection points
    for (let x = 100; x <= 400; x += 40) {
      for (let y = 100; y <= 300; y += 40) {
        points.push(addPoint(canvas, x, y, pointColor, pointRadius));
      }
    }

    // Border intersection points
    for (let x = 100; x <= 400; x += 60) {
      points.push(addPoint(canvas, x, 40, pointColor, pointRadius));
      points.push(addPoint(canvas, x, 360, pointColor, pointRadius));
    }

    for (let y = 100; y <= 300; y += 60) {
      points.push(addPoint(canvas, 60, y, pointColor, pointRadius));
      points.push(addPoint(canvas, 440, y, pointColor, pointRadius));
    }

    return points;
  };

  // Draw ground symbol in the center bottom
  const drawGroundSymbol = (canvas: fabric.Canvas) => {
    const groundColor = '#00ff9d';
    const lineWidth = 1.5;

    // Vertical line
    addLine(canvas, 250, 240, 250, 260, groundColor, lineWidth);

    // Three horizontal lines (ground symbol)
    addLine(canvas, 240, 260, 260, 260, groundColor, lineWidth);
    addLine(canvas, 235, 265, 265, 265, groundColor, lineWidth);
    addLine(canvas, 230, 270, 270, 270, groundColor, lineWidth);
  };

  // Helper function to add a line
  const addLine = (canvas: fabric.Canvas, x1: number, y1: number, x2: number, y2: number, color: string, width: number) => {
    const line = new fabric.Line([x1, y1, x2, y2], {
      stroke: color,
      strokeWidth: width,
      selectable: false
    });
    canvas.add(line);
    return line;
  };

  // Helper function to add a rectangle
  const addRectangle = (canvas: fabric.Canvas, x: number, y: number, width: number, height: number, color: string, strokeWidth: number) => {
    const rect = new fabric.Rect({
      left: x,
      top: y,
      width: width,
      height: height,
      fill: 'transparent',
      stroke: color,
      strokeWidth: strokeWidth,
      selectable: false
    });
    canvas.add(rect);
    return rect;
  };

  // Helper function to add a point
  const addPoint = (canvas: fabric.Canvas, x: number, y: number, color: string, radius: number) => {
    const point = new fabric.Circle({
      left: x - radius,
      top: y - radius,
      radius: radius,
      fill: color,
      selectable: false
    });
    canvas.add(point);
    return point;
  };

  useEffect(() => {
    // Fetch latest diagram data
    const fetchHardwareData = async () => {
      try {
        // For now, let's use a mock diagram data since the hardware_diagrams table doesn't exist
        setLatestDiagram({
          version: "1.0",
          name: "Circuit Diagram",
          link: "/downloads/circuit-diagram.pdf"
        });

        // When the hardware_diagrams table exists, you can uncomment this code:
        /*
        const { data, error } = await supabase
          .from('hardware_diagrams')
          .select('version, name, link')
          .order('release_at', { ascending: false })
          .limit(1);

        if (error) throw error;
        if (data && data.length > 0) {
          setLatestDiagram(data[0]);
        }
        */
      } catch (error) {
        console.error('Error fetching hardware data:', error);
      }
    };

    fetchHardwareData();
  }, []);

  const handleDownload = () => {
    if (latestDiagram?.link) {
      window.location.href = latestDiagram.link;
      toast.success('Download started!');
    } else {
      toast.info("Download link is not available at the moment. Please try again later.");
    }
  };

  return (
    <section className="pt-28 pb-20 bg-gradient-to-br from-green-50/95 via-green-100/85 to-green-200/75 dark:from-gray-900/98 dark:via-gray-800/95 dark:to-green-900/30 overflow-hidden relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-[0.03]">
        <div className="absolute inset-0 bg-[url('/patterns/dots.svg')]"></div>
      </div>

      {/* Animated Gradient Overlay */}
      <div className="absolute inset-0 opacity-100 transition-opacity duration-1000 bg-gradient-to-br from-green-400/8 via-green-300/5 to-green-600/12"></div>

      {/* Subtle Texture Overlay */}
      <div className="absolute inset-0 opacity-[0.02] bg-gradient-to-br from-white via-transparent to-gray-100 dark:from-gray-700 dark:via-transparent dark:to-gray-800"></div>

      {/* Animated background elements */}
      <motion.div
        className="absolute top-20 right-20 w-64 h-64 bg-green-500/5 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.2, 0.3]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      ></motion.div>
      <motion.div
        className="absolute bottom-10 left-10 w-40 h-40 bg-green-500/10 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.5, 1],
          opacity: [0.2, 0.3, 0.2]
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      ></motion.div>

      <div className="container mx-auto px-4 relative">
        <div className="flex flex-col md:flex-row items-center">
          <motion.div
            className="md:w-1/2 relative z-10"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.7, ease: "easeOut" }}
          >
            <div className="relative flex items-center justify-center">
              <motion.div
                className="bg-gradient-to-br from-green-700/30 to-green-900/20 rounded-full h-64 w-64 md:h-96 md:w-96 mx-auto absolute"
                animate={{
                  scale: [1, 1.05, 1],
                  opacity: [0.7, 0.9, 0.7]
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              ></motion.div>
              <motion.div
                ref={hardwareImageRef}
                className="relative z-10 max-w-full md:max-w-md mx-auto bg-black/20 rounded-lg p-4 preserve-3d"
                animate={{
                  y: [0, -10, 0],
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                style={{
                  maxHeight: '500px',
                }}
              >
                <canvas
                  ref={canvasRef}
                  className="w-full h-full object-contain"
                  width="500"
                  height="400"
                  style={{
                    filter: 'drop-shadow(0 10px 15px rgb(0 0 0 / 0.5))'
                  }}
                />

                {/* Glowing circuit points */}
                <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <div className="absolute top-1/2 left-1/3 w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <div className="absolute bottom-1/3 right-1/4 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              </motion.div>
            </div>
          </motion.div>

          <motion.div
            className="md:w-1/2 mb-10 md:mb-0"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7, ease: "easeOut" }}
          >
            <Text3D
              as="h1"
              size="4xl"
              depth="deep"
              color="text-green-500"
              shadowColor="text-green-700"
              className="mb-6 font-montserrat tracking-tight leading-tight"
            >
              Hardware <span className="text-green-400">Diagrams</span> & Components
            </Text3D>

            <motion.p
              className="text-lg md:text-xl text-gray-800 dark:text-gray-200 mb-8 max-w-lg"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.7 }}
            >
              Explore detailed circuit diagrams, connection paths, and components like capacitors
              and ICs. Everything you need for hardware-level diagnostics and repair.
            </motion.p>

            <motion.div
              className="space-y-4 md:space-y-0 md:space-x-4 flex flex-col md:flex-row"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.7 }}
            >
              <Button
                className="bg-green-500 hover:bg-green-600 text-white px-6 py-6 rounded-full text-lg w-full md:w-auto transition-all duration-300 hover:-translate-y-1 shadow-lg hover:shadow-xl flex items-center justify-center group"
                onClick={handleDownload}
              >
                <Download className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                <span className="relative">
                  Download Diagrams {latestDiagram && `- V${latestDiagram.version}`}
                  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-white/30 scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></span>
                </span>
              </Button>

              <Button
                className="bg-transparent border-2 border-green-500 text-white px-6 py-6 rounded-full text-lg w-full md:w-auto transition-all duration-300 hover:-translate-y-1 shadow-lg hover:shadow-xl hover:bg-green-600 flex items-center justify-center group"
                onClick={() => document.getElementById('circuit-diagrams')?.scrollIntoView({ behavior: 'smooth' })}
              >
                <ArrowRight className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                <span className="relative">
                  Learn More
                  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-white/30 scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></span>
                </span>
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Bottom Accent Line */}
      <motion.div
        className="absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r from-transparent via-green-400 to-transparent"
        initial={{ scaleX: 0 }}
        whileInView={{ scaleX: 1 }}
        transition={{ duration: 1.5, delay: 0.5 }}
        viewport={{ once: true }}
      />
    </section>
  );
};

export default HardwareHero;
