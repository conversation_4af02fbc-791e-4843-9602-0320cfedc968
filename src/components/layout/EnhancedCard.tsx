import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { getTheme, motionVariants, spacing, ThemeType } from '@/styles/design-system';

interface EnhancedCardProps {
  theme?: ThemeType;
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'glass' | 'elevated' | 'minimal';
  size?: 'small' | 'normal' | 'large';
  withGlow?: boolean;
  withHover?: boolean;
  withBorder?: boolean;
  animate?: boolean;
  delay?: number;
}

const EnhancedCard: React.FC<EnhancedCardProps> = ({
  theme = 'neutral',
  children,
  className,
  variant = 'default',
  size = 'normal',
  withGlow = false,
  withHover = true,
  withBorder = true,
  animate = true,
  delay = 0
}) => {
  const themeConfig = getTheme(theme);

  const getVariantClasses = () => {
    switch (variant) {
      case 'glass':
        return cn(
          themeConfig.glass,
          'border backdrop-blur-2xl',
          withBorder && themeConfig.border,
          'shadow-lg'
        );
      case 'elevated':
        return cn(
          themeConfig.cardGradient,
          'shadow-2xl border',
          withBorder && themeConfig.border,
          withGlow && themeConfig.glow,
          'ring-1 ring-white/20 dark:ring-gray-700/20'
        );
      case 'minimal':
        return cn(
          'bg-gray-50/80 dark:bg-gray-800/80',
          'border border-gray-200/60 dark:border-gray-600/60',
          'shadow-md backdrop-blur-sm'
        );
      default:
        return cn(
          themeConfig.cardGradient,
          'border',
          withBorder && themeConfig.border,
          themeConfig.shadow,
          'ring-1 ring-white/10 dark:ring-gray-700/10'
        );
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return spacing.cardSmall;
      case 'large':
        return 'p-10';
      default:
        return spacing.card;
    }
  };

  const getHoverClasses = () => {
    if (!withHover) return '';
    return cn(
      'transition-all duration-300 ease-out',
      'hover:-translate-y-2 hover:scale-[1.02]',
      themeConfig.hoverShadow,
      withGlow && `hover:${themeConfig.glow}`
    );
  };

  const cardContent = (
    <div
      className={cn(
        'relative overflow-hidden rounded-2xl',
        getVariantClasses(),
        getSizeClasses(),
        getHoverClasses(),
        className
      )}
    >
      {/* Decorative Elements */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div className="absolute inset-0 bg-[url('/patterns/dots.svg')]"></div>
      </div>

      {/* Gradient Overlay */}
      <div className={cn(
        'absolute inset-0 opacity-60 transition-opacity duration-500',
        themeConfig.overlayGradient
      )}></div>

      {/* Corner Accent */}
      <div className={cn(
        'absolute top-0 right-0 w-24 h-24 opacity-15 rounded-bl-3xl',
        theme === 'hardware' ? 'bg-gradient-to-bl from-green-300/40 via-green-400/20 to-transparent' :
        theme === 'software' ? 'bg-gradient-to-bl from-orange-300/40 via-orange-400/20 to-transparent' :
        'bg-gradient-to-bl from-gray-300/40 via-gray-400/20 to-transparent'
      )}></div>

      {/* Bottom Left Accent */}
      <div className={cn(
        'absolute bottom-0 left-0 w-16 h-16 opacity-10 rounded-tr-2xl',
        theme === 'hardware' ? 'bg-gradient-to-tr from-green-200/30 to-transparent' :
        theme === 'software' ? 'bg-gradient-to-tr from-orange-200/30 to-transparent' :
        'bg-gradient-to-tr from-gray-200/30 to-transparent'
      )}></div>

      {/* Subtle Inner Border */}
      <div className="absolute inset-[1px] rounded-2xl border border-white/20 dark:border-gray-700/20 pointer-events-none"></div>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>

      {/* Interactive Glow Effect */}
      {withHover && (
        <div className={cn(
          'absolute inset-0 rounded-2xl opacity-0 transition-opacity duration-300',
          'hover:opacity-100 pointer-events-none',
          theme === 'hardware' ? 'shadow-[inset_0_0_30px_rgba(34,197,94,0.15)]' :
          theme === 'software' ? 'shadow-[inset_0_0_30px_rgba(251,146,60,0.15)]' :
          'shadow-[inset_0_0_30px_rgba(107,114,128,0.15)]'
        )}></div>
      )}
    </div>
  );

  if (!animate) {
    return cardContent;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      whileInView={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 0.6,
        ease: "easeOut",
        delay: delay
      }}
      viewport={{ once: true, margin: "-50px" }}
      whileHover={{
        scale: withHover ? 1.02 : 1,
        transition: { duration: 0.2 }
      }}
    >
      {cardContent}
    </motion.div>
  );
};

export default EnhancedCard;
