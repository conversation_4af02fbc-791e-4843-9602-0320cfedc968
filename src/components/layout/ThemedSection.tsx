import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { getTheme, motionVariants, spacing, ThemeType } from '@/styles/design-system';

interface ThemedSectionProps {
  theme?: ThemeType;
  children: React.ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary' | 'card' | 'glass';
  size?: 'normal' | 'large';
  withPattern?: boolean;
  withParallax?: boolean;
  id?: string;
  animate?: boolean;
  animationVariant?: 'fadeInUp' | 'fadeInLeft' | 'fadeInRight' | 'scaleIn';
}

const ThemedSection: React.FC<ThemedSectionProps> = ({
  theme = 'neutral',
  children,
  className,
  variant = 'primary',
  size = 'normal',
  withPattern = true,
  withParallax = false,
  id,
  animate = true,
  animationVariant = 'fadeInUp'
}) => {
  const themeConfig = getTheme(theme);

  const getBackgroundClass = () => {
    switch (variant) {
      case 'secondary':
        return themeConfig.secondaryGradient;
      case 'card':
        return themeConfig.cardGradient;
      case 'glass':
        return themeConfig.glass;
      default:
        return themeConfig.primaryGradient;
    }
  };

  const getSizeClass = () => {
    return size === 'large' ? spacing.sectionLarge : spacing.section;
  };

  const sectionContent = (
    <section
      id={id}
      className={cn(
        'relative overflow-hidden',
        getSizeClass(),
        getBackgroundClass(),
        className
      )}
    >
      {/* Background Pattern */}
      {withPattern && (
        <div className="absolute inset-0 opacity-[0.03]">
          <div className="absolute inset-0 bg-[url('/patterns/dots.svg')]"></div>
        </div>
      )}

      {/* Animated Gradient Overlay */}
      <div className={cn(
        'absolute inset-0 opacity-100 transition-opacity duration-1000',
        themeConfig.overlayGradient
      )}></div>

      {/* Subtle Texture Overlay */}
      <div className="absolute inset-0 opacity-[0.02] bg-gradient-to-br from-white via-transparent to-gray-100 dark:from-gray-700 dark:via-transparent dark:to-gray-800"></div>

      {/* Parallax Background Elements */}
      {withParallax && (
        <>
          <motion.div
            className={cn(
              'absolute top-10 right-10 w-96 h-96 rounded-full blur-3xl opacity-20',
              theme === 'hardware' ? 'bg-gradient-to-br from-green-300/30 to-green-500/20' :
              theme === 'software' ? 'bg-gradient-to-br from-orange-300/30 to-orange-500/20' :
              'bg-gradient-to-br from-gray-300/30 to-gray-500/20'
            )}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.15, 0.25, 0.15],
              rotate: [0, 180, 360]
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className={cn(
              'absolute bottom-10 left-10 w-72 h-72 rounded-full blur-3xl opacity-15',
              theme === 'hardware' ? 'bg-gradient-to-tl from-green-400/25 to-green-600/15' :
              theme === 'software' ? 'bg-gradient-to-tl from-orange-400/25 to-orange-600/15' :
              'bg-gradient-to-tl from-gray-400/25 to-gray-600/15'
            )}
            animate={{
              scale: [1, 1.4, 1],
              opacity: [0.1, 0.2, 0.1],
              rotate: [360, 180, 0]
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 3
            }}
          />
          <motion.div
            className={cn(
              'absolute top-1/2 left-1/2 w-32 h-32 rounded-full blur-2xl opacity-10 -translate-x-1/2 -translate-y-1/2',
              theme === 'hardware' ? 'bg-green-200/40' :
              theme === 'software' ? 'bg-orange-200/40' : 'bg-gray-200/40'
            )}
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.05, 0.15, 0.05]
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 1
            }}
          />
        </>
      )}

      {/* Content Container */}
      <div className={cn('relative z-10', spacing.container)}>
        {children}
      </div>

      {/* Bottom Accent Line */}
      <motion.div
        className={cn(
          'absolute bottom-0 left-0 h-1 w-full',
          theme === 'hardware' ? 'bg-gradient-to-r from-transparent via-green-400 to-transparent' :
          theme === 'software' ? 'bg-gradient-to-r from-transparent via-orange-400 to-transparent' :
          'bg-gradient-to-r from-transparent via-gray-400 to-transparent'
        )}
        initial={{ scaleX: 0 }}
        whileInView={{ scaleX: 1 }}
        transition={{ duration: 1.5, delay: 0.5 }}
        viewport={{ once: true }}
      />
    </section>
  );

  if (!animate) {
    return sectionContent;
  }

  return (
    <motion.div
      initial={motionVariants[animationVariant].initial}
      whileInView={motionVariants[animationVariant].animate}
      transition={motionVariants[animationVariant].transition}
      viewport={{ once: true, margin: "-100px" }}
    >
      {sectionContent}
    </motion.div>
  );
};

export default ThemedSection;
