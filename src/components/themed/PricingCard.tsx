import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Star, Shield, Zap } from 'lucide-react';
import ThemedCard from './ThemedCard';
import Pricing from '@/sections/Pricing';

interface PricingCardProps {
  theme?: 'software' | 'hardware';
  className?: string;
}

const PricingCard: React.FC<PricingCardProps> = ({
  theme = 'software',
  className
}) => {
  const getThemeConfig = () => {
    if (theme === 'hardware') {
      return {
        title: "Hardware Solutions Pricing",
        subtitle: "Professional hardware documentation and repair guides",
        icon: "⚡",
        accentColor: "text-green-600 dark:text-green-400",
        features: [
          { icon: Shield, text: "Circuit Diagrams & Schematics" },
          { icon: Zap, text: "Component Specifications" },
          { icon: Star, text: "Professional Support" }
        ]
      };
    }
    return {
      title: "Software Solutions Pricing",
      subtitle: "Complete software tools for device unlocking and repair",
      icon: "💎",
      accentColor: "text-pegasus-orange",
      features: [
        { icon: Shield, text: "Advanced Unlocking Tools" },
        { icon: Zap, text: "Real-time Updates" },
        { icon: Star, text: "24/7 Technical Support" }
      ]
    };
  };

  const config = getThemeConfig();

  return (
    <div className={cn("w-full", className)}>
      <ThemedCard
        theme={theme}
        title={config.title}
        subtitle={config.subtitle}
        className="max-w-7xl mx-auto"
      >
        {/* Features highlight section */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          {config.features.map((feature, index) => (
            <motion.div
              key={index}
              className={cn(
                "flex flex-col items-center gap-4 p-6 rounded-2xl border backdrop-blur-xl text-center",
                theme === 'hardware'
                  ? "bg-gradient-to-br from-green-50/90 to-white/80 dark:from-green-900/30 dark:to-gray-800/70 border-green-200/50 dark:border-green-700/40 hover:from-green-100/90 hover:to-white/90"
                  : "bg-gradient-to-br from-orange-50/90 to-white/80 dark:from-orange-900/30 dark:to-gray-800/70 border-orange-200/50 dark:border-orange-700/40 hover:from-orange-100/90 hover:to-white/90",
                "shadow-lg ring-1 ring-white/20 dark:ring-gray-700/20 transition-all duration-300"
              )}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05, y: -5 }}
            >
              <div className={cn(
                "p-4 rounded-2xl shadow-md",
                theme === 'hardware'
                  ? "bg-gradient-to-br from-green-100 to-green-200 dark:from-green-800/40 dark:to-green-900/30"
                  : "bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-800/40 dark:to-orange-900/30"
              )}>
                <feature.icon className={cn("h-8 w-8", config.accentColor)} />
              </div>
              <div>
                <h4 className="font-bold text-lg mb-2 text-gray-800 dark:text-gray-200">
                  {feature.text}
                </h4>
                <div className={cn(
                  "w-12 h-1 rounded-full mx-auto",
                  theme === 'hardware'
                    ? "bg-gradient-to-r from-green-400 to-green-500"
                    : "bg-gradient-to-r from-orange-400 to-orange-500"
                )}></div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Main pricing content */}
        <div className="relative">
          <Pricing theme={theme} />
        </div>

        {/* Bottom call-to-action section */}
        <motion.div
          className={cn(
            "mt-12 p-8 rounded-2xl border backdrop-blur-xl relative overflow-hidden",
            theme === 'hardware'
              ? "border-green-300/60 dark:border-green-600/40 bg-gradient-to-br from-green-50/80 to-green-100/60 dark:from-green-900/30 dark:to-green-800/20"
              : "border-orange-300/60 dark:border-orange-600/40 bg-gradient-to-br from-orange-50/80 to-orange-100/60 dark:from-orange-900/30 dark:to-orange-800/20",
            "shadow-xl ring-1 ring-white/20 dark:ring-gray-700/20"
          )}
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          viewport={{ once: true }}
          whileHover={{ scale: 1.02 }}
        >
          {/* Background decoration */}
          <div className={cn(
            "absolute top-0 right-0 w-32 h-32 rounded-full blur-2xl opacity-20",
            theme === 'hardware' ? "bg-green-300" : "bg-orange-300"
          )}></div>

          <div className="text-center relative z-10">
            <div className={cn(
              "text-4xl mb-4 p-4 rounded-2xl inline-block",
              theme === 'hardware'
                ? "bg-gradient-to-br from-green-100 to-green-200 dark:from-green-800/40 dark:to-green-900/30"
                : "bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-800/40 dark:to-orange-900/30"
            )}>
              {config.icon}
            </div>
            <h4 className={cn("text-2xl font-bold mb-3", config.accentColor)}>
              Need a Custom Solution?
            </h4>
            <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed max-w-2xl mx-auto">
              Contact our team for enterprise pricing and custom integrations tailored to your business needs. We provide personalized solutions for large-scale deployments.
            </p>
            <div className={cn(
              "w-24 h-1 rounded-full mx-auto mt-4",
              theme === 'hardware'
                ? "bg-gradient-to-r from-green-400 to-green-500"
                : "bg-gradient-to-r from-orange-400 to-orange-500"
            )}></div>
          </div>
        </motion.div>
      </ThemedCard>
    </div>
  );
};

export default PricingCard;
