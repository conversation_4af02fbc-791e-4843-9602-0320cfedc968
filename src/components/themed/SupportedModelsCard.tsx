import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import ThemedCard from './ThemedCard';
import SupportedModels from '@/sections/SupportedModels';

interface SupportedModelsCardProps {
  theme?: 'software' | 'hardware';
  className?: string;
}

const SupportedModelsCard: React.FC<SupportedModelsCardProps> = ({
  theme = 'software',
  className
}) => {
  const getThemeConfig = () => {
    if (theme === 'hardware') {
      return {
        title: "Supported Device Models",
        subtitle: "Comprehensive hardware compatibility for professional repair and diagnostics",
        icon: "🔧",
        accentColor: "text-green-600 dark:text-green-400"
      };
    }
    return {
      title: "Supported Device Models",
      subtitle: "Complete software compatibility for unlocking and repair solutions",
      icon: "📱",
      accentColor: "text-pegasus-orange"
    };
  };

  const config = getThemeConfig();

  return (
    <div className={cn("w-full", className)}>
      <ThemedCard
        theme={theme}
        title={config.title}
        subtitle={config.subtitle}
        className="max-w-7xl mx-auto"
      >
        {/* Icon and description section */}
        <motion.div
          className={cn(
            "flex items-center gap-6 mb-8 p-8 rounded-2xl border backdrop-blur-xl",
            theme === 'hardware'
              ? "bg-gradient-to-r from-green-50/80 to-white/70 dark:from-green-900/20 dark:to-gray-800/60 border-green-200/40 dark:border-green-700/30"
              : "bg-gradient-to-r from-orange-50/80 to-white/70 dark:from-orange-900/20 dark:to-gray-800/60 border-orange-200/40 dark:border-orange-700/30",
            "shadow-lg ring-1 ring-white/20 dark:ring-gray-700/20"
          )}
          initial={{ opacity: 0, x: -20 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          viewport={{ once: true }}
          whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
        >
          <div className={cn(
            "text-5xl p-4 rounded-2xl",
            theme === 'hardware'
              ? "bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900/40 dark:to-green-800/30"
              : "bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/40 dark:to-orange-800/30"
          )}>
            {config.icon}
          </div>
          <div className="flex-1">
            <h3 className={cn("text-2xl font-bold mb-2", config.accentColor)}>
              Professional Device Support
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
              Browse our extensive database of supported devices with detailed compatibility information and professional-grade specifications
            </p>
          </div>
        </motion.div>

        {/* SupportedModels component */}
        <div className="relative">
          <SupportedModels theme={theme} />
        </div>

        {/* Bottom accent bar with enhanced design */}
        <motion.div
          className="mt-12 relative"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <motion.div
            className={cn(
              "h-2 rounded-full relative overflow-hidden",
              theme === 'hardware'
                ? "bg-gradient-to-r from-green-300 via-green-500 to-green-400"
                : "bg-gradient-to-r from-orange-300 via-orange-500 to-orange-400"
            )}
            initial={{ scaleX: 0 }}
            whileInView={{ scaleX: 1 }}
            transition={{ duration: 1.2, delay: 0.6 }}
            viewport={{ once: true }}
          >
            {/* Animated shimmer effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
              animate={{
                x: ['-100%', '100%']
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatDelay: 3
              }}
            />
          </motion.div>
        </motion.div>
      </ThemedCard>
    </div>
  );
};

export default SupportedModelsCard;
