
import { useEffect, useRef, useState } from 'react';

interface InViewOptions {
  threshold?: number;
  triggerOnce?: boolean;
  rootMargin?: string;
  delay?: number;
}

/**
 * Enhanced hook to detect when an element is in the viewport
 * with additional options for animation control
 */
export const useInView = ({
  threshold = 0.1,
  triggerOnce = false,
  rootMargin = '0px',
  delay = 0
}: InViewOptions = {}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [inView, setInView] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);

  useEffect(() => {
    const currentRef = ref.current;
    if (!currentRef) return;

    let timeoutId: number;

    const observer = new IntersectionObserver(
      ([entry]) => {
        // Only proceed if we haven't triggered once, or if triggerOnce is false
        if (hasTriggered && triggerOnce) return;

        // Update state when intersection status changes
        const isIntersecting = entry.isIntersecting;
        
        if (isIntersecting) {
          if (delay > 0) {
            // Apply delay if specified
            timeoutId = window.setTimeout(() => {
              setInView(true);
              setHasTriggered(true);
            }, delay);
          } else {
            setInView(true);
            setHasTriggered(true);
          }
          
          // Unobserve if triggerOnce is true
          if (triggerOnce) {
            observer.unobserve(currentRef);
          }
        } else {
          // Only reset inView if triggerOnce is false
          if (!triggerOnce) {
            setInView(false);
            clearTimeout(timeoutId);
          }
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    observer.observe(currentRef);

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
      clearTimeout(timeoutId);
    };
  }, [threshold, triggerOnce, rootMargin, delay, hasTriggered]);

  return [ref, inView] as const;
};
