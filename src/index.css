@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Loading animation styles */
/* Loading animation styles */
body.loading {
  overflow: hidden;
}

body.loading #root {
  display: none;
}

body.loaded #loading-root {
  display: none;
}

/* Circular reveal animation styles */
#mask-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  pointer-events: none;
  transition: opacity 0.5s ease;
}

/* تحديد موضع الدوائر بدقة */
.loading-ripple,
.loading-small-ripple {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  pointer-events: none !important;
  z-index: 10000 !important;
}

/* منع ظهور أي دوائر إضافية */
.loading-ripple ~ .loading-ripple,
.loading-small-ripple ~ .loading-small-ripple {
  display: none !important;
}

@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(100);
    opacity: 0;
  }
}

@keyframes draw-path {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes fill-color {
  to {
    fill-opacity: 1;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 24 95% 53%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 24 95% 53%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground scroll-smooth will-change-scroll;
    font-family: 'Inter', 'Cairo', 'Montserrat', sans-serif;
  }
  
  html {
    scroll-behavior: smooth;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', 'Cairo', sans-serif;
    @apply font-bold tracking-tight;
  }

  /* Enhanced glass-like card with hover effect */
  .glass-card {
    @apply backdrop-blur-lg bg-white/10 dark:bg-black/20 border border-white/20 dark:border-white/10 rounded-xl shadow-xl transition-all duration-500;
    transform-style: preserve-3d;
    perspective: 1000px;
  }
  
  .glass-card:hover {
    @apply shadow-2xl bg-white/20 dark:bg-black/30 border-white/30 dark:border-white/20;
    transform: translateY(-5px);
  }
  
  /* Enhanced 3D button with hover states */
  .button-3d {
    @apply relative overflow-hidden bg-pegasus-orange text-white px-8 py-3 rounded-xl font-medium transition-all duration-300 shadow-lg;
    transform-style: preserve-3d;
    backface-visibility: hidden;
  }
  
  .button-3d:before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 transition-opacity duration-300;
  }
  
  .button-3d:hover {
    @apply shadow-xl -translate-y-1;
    transform: translateY(-5px) scale(1.02);
  }
  
  .button-3d:hover:before {
    @apply opacity-100;
  }
  
  .button-3d:active {
    @apply shadow-md translate-y-0;
    transform: translateY(2px) scale(0.98);
  }

  /* Enhanced section titles with visual accent */
  .title-accent {
    @apply relative inline-block;
  }
  
  .title-accent:after {
    content: '';
    @apply absolute -bottom-2 left-0 w-1/3 h-1 bg-pegasus-orange rounded-full;
  }
  
  /* Enhanced hover link with underline animation */
  .hover-link {
    @apply relative inline-block transition-colors duration-300;
  }
  
  .hover-link:after {
    content: '';
    @apply absolute bottom-0 left-0 w-full h-0.5 bg-pegasus-orange scale-x-0 origin-bottom-right transition-transform duration-300;
  }
  
  .hover-link:hover:after {
    @apply scale-x-100 origin-bottom-left;
  }
  
  /* Enhanced card with depth and hover animations */
  .depth-card {
    @apply bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg transition-all duration-300;
    transform: perspective(1000px) rotateX(0) rotateY(0);
    transform-style: preserve-3d;
  }
  
  .depth-card:hover {
    @apply shadow-xl;
    transform: perspective(1000px) rotateX(2deg) rotateY(2deg) translateY(-5px);
  }
}

/* Enhanced animation classes with improved transitions */
.animate-fade-in {
  animation: enhanced-fade-in 0.8s cubic-bezier(0.22, 1, 0.36, 1) forwards;
}

.animate-fade-in-delay-1 {
  animation: enhanced-fade-in 0.8s cubic-bezier(0.22, 1, 0.36, 1) 0.1s forwards;
}

.animate-fade-in-delay-2 {
  animation: enhanced-fade-in 0.8s cubic-bezier(0.22, 1, 0.36, 1) 0.2s forwards;
}

.animate-fade-in-delay-3 {
  animation: enhanced-fade-in 0.8s cubic-bezier(0.22, 1, 0.36, 1) 0.3s forwards;
}

.animate-fade-in-delay-4 {
  animation: enhanced-fade-in 0.8s cubic-bezier(0.22, 1, 0.36, 1) 0.4s forwards;
}

.animate-fade-in-delay-5 {
  animation: enhanced-fade-in 0.8s cubic-bezier(0.22, 1, 0.36, 1) 0.5s forwards;
}

.animate-slide-in {
  animation: enhanced-slide-in 0.8s cubic-bezier(0.22, 1, 0.36, 1) forwards;
}

/* Updated and enhanced animations */
@keyframes enhanced-fade-in {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  70% {
    opacity: 0.9;
    transform: translateY(-5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes enhanced-fade-in-right {
  0% {
    opacity: 0;
    transform: translateX(30px) scale(0.95);
  }
  70% {
    opacity: 0.9;
    transform: translateX(-5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes enhanced-slide-in {
  0% {
    opacity: 0;
    transform: translateX(-40px) scale(0.95);
  }
  75% {
    opacity: 0.9;
    transform: translateX(8px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes enhanced-float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) rotate(1.5deg);
  }
  50% {
    transform: translateY(-20px) rotate(-1.5deg);
  }
  75% {
    transform: translateY(-10px) rotate(0.5deg);
  }
}

@keyframes enhanced-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(0.96);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* New marquee animation for scrolling content */
@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

.marquee {
  white-space: nowrap;
  overflow: hidden;
}

.marquee-content {
  display: inline-block;
  animation: marquee 30s linear infinite;
}

/* New staggered animation for lists */
.stagger-item {
  opacity: 0;
  transform: translateY(20px);
}

.stagger-show .stagger-item {
  animation: stagger-in 0.6s ease forwards;
}

.stagger-show .stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-show .stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-show .stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-show .stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-show .stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-show .stagger-item:nth-child(6) { animation-delay: 0.6s; }
.stagger-show .stagger-item:nth-child(7) { animation-delay: 0.7s; }
.stagger-show .stagger-item:nth-child(8) { animation-delay: 0.8s; }
.stagger-show .stagger-item:nth-child(9) { animation-delay: 0.9s; }
.stagger-show .stagger-item:nth-child(10) { animation-delay: 1s; }

@keyframes stagger-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced text blur-in animation */
.text-blur-in {
  filter: blur(12px);
  opacity: 0;
  animation: text-blur-in 1s cubic-bezier(0.22, 1, 0.36, 1) forwards;
}

@keyframes text-blur-in {
  0% {
    filter: blur(12px);
    opacity: 0;
  }
  100% {
    filter: blur(0);
    opacity: 1;
  }
}

/* Enhanced typewriter effect */
.typewriter {
  overflow: hidden;
  border-right: 3px solid var(--primary);
  white-space: nowrap;
  letter-spacing: 0.1em;
  animation: typing 3.5s steps(30, end), blink-caret 0.75s step-end infinite;
  max-width: fit-content;
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink-caret {
  from, to { border-color: transparent }
  50% { border-color: var(--primary) }
}

/* New reveal animations */
.reveal-right {
  position: relative;
  overflow: hidden;
}

.reveal-right::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--primary);
  transform: translateX(-100%);
  animation: reveal-right 1.2s cubic-bezier(0.77, 0, 0.175, 1) forwards;
}

@keyframes reveal-right {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Enhanced glass morphism */
.glassmorphism {
  @apply backdrop-blur-lg bg-white/10 dark:bg-black/20 border border-white/20 dark:border-white/10 shadow-lg;
}

/* New gradient text */
.gradient-text {
  @apply bg-gradient-to-r from-pegasus-orange via-pegasus-orange-500 to-pegasus-orange-700 bg-clip-text text-transparent;
  animation: gradient-shift 8s ease infinite;
  background-size: 200% 200%;
}

/* Transform GPU utilities for smoother animations */
.transform-gpu {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Enhanced section spacing */
.section-padding {
  @apply py-20 md:py-28 px-4;
}

/* 3D Specific Utility Classes */
.perspective-1000 {
  perspective: 1000px;
}

.perspective-2000 {
  perspective: 2000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.transform-3d {
  transform: translateZ(0);
  will-change: transform;
}

.text-3d {
  text-shadow: 0 1px 0 #ccc, 
               0 2px 0 #c9c9c9,
               0 3px 0 #bbb,
               0 4px 0 #b9b9b9,
               0 5px 0 #aaa,
               0 6px 1px rgba(0,0,0,.1),
               0 0 5px rgba(0,0,0,.1),
               0 1px 3px rgba(0,0,0,.3),
               0 3px 5px rgba(0,0,0,.2),
               0 5px 10px rgba(0,0,0,.25),
               0 10px 10px rgba(0,0,0,.2),
               0 20px 20px rgba(0,0,0,.15);
}

.text-3d-light {
  text-shadow: 0 1px 0 rgba(255,255,255,.2), 
               0 2px 3px rgba(0,0,0,.3);
}

.shadow-3d {
  box-shadow: 0 10px 20px rgba(0,0,0,0.19), 
              0 6px 6px rgba(0,0,0,0.23);
}

.shadow-3d-hover {
  transition: all 0.3s cubic-bezier(.25,.8,.25,1);
}

.shadow-3d-hover:hover {
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 
              0 10px 10px rgba(0,0,0,0.22);
}

.card-3d {
  @apply bg-white dark:bg-gray-800 rounded-xl p-6 transition-all duration-300;
  transform-style: preserve-3d;
  transform: perspective(1000px);
}

.card-3d::before {
  content: '';
  @apply absolute inset-0 -z-10 rounded-xl;
  transform: translateZ(-10px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.card-3d:hover {
  transform: perspective(1000px) rotateX(2deg) rotateY(2deg) translateZ(10px);
}

.rotating-3d {
  animation: rotate3d 20s linear infinite;
}

@keyframes rotate3d {
  0% { transform: rotateX(0deg) rotateY(0deg); }
  100% { transform: rotateX(360deg) rotateY(360deg); }
}

.hover-lift-3d {
  transition: transform 0.3s ease;
}

.hover-lift-3d:hover {
  transform: translateZ(20px);
}

.floating-3d {
  animation: float3d 6s ease-in-out infinite;
}

@keyframes float3d {
  0%, 100% {
    transform: translateY(0) translateZ(0) rotateX(0deg) rotateY(0deg);
  }
  25% {
    transform: translateY(-10px) translateZ(5px) rotateX(2deg) rotateY(-2deg);
  }
  50% {
    transform: translateY(-20px) translateZ(10px) rotateX(4deg) rotateY(2deg);
  }
  75% {
    transform: translateY(-10px) translateZ(15px) rotateX(2deg) rotateY(-1deg);
  }
}
