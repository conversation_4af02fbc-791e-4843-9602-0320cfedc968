
/**
 * Utility functions for 3D effects and animations
 */

// Calculate perspective depth based on mouse position
export const calculatePerspective = (
  e: MouseEvent, 
  element: HTMLElement,
  intensity: number = 20
): { rotateX: number; rotateY: number } => {
  const rect = element.getBoundingClientRect();
  const centerX = rect.left + rect.width / 2;
  const centerY = rect.top + rect.height / 2;

  // Calculate mouse position relative to center of element
  const mouseX = e.clientX - centerX;
  const mouseY = e.clientY - centerY;

  // Calculate rotation based on distance from center (with intensity factor)
  const rotateY = (mouseX / (rect.width / 2)) * intensity;
  const rotateX = ((mouseY / (rect.height / 2)) * -intensity);

  return { rotateX, rotateY };
};

// Calculate depth layers for staggered 3D effect
export const calculateDepthLayers = (
  count: number, 
  minDepth: number = -100,
  maxDepth: number = 0
): number[] => {
  const layers = [];
  const step = (maxDepth - minDepth) / (count - 1);
  
  for (let i = 0; i < count; i++) {
    layers.push(minDepth + (step * i));
  }
  
  return layers;
};

// Generate CSS transform for 3D space
export const generateTransform3D = ({
  x = 0,
  y = 0,
  z = 0,
  rotateX = 0,
  rotateY = 0,
  rotateZ = 0,
  scale = 1
}: {
  x?: number;
  y?: number;
  z?: number;
  rotateX?: number;
  rotateY?: number;
  rotateZ?: number;
  scale?: number;
}) => {
  return `
    translate3d(${x}px, ${y}px, ${z}px) 
    rotateX(${rotateX}deg) 
    rotateY(${rotateY}deg) 
    rotateZ(${rotateZ}deg) 
    scale(${scale})
  `;
};

// Create shadow projection based on transform
export const generateShadowProjection = ({
  rotateX,
  rotateY,
  distance = 10,
  blur = 10,
  opacity = 0.2,
  color = 'rgba(0,0,0,0.2)'
}: {
  rotateX: number;
  rotateY: number;
  distance?: number;
  blur?: number;
  opacity?: number;
  color?: string;
}) => {
  // Calculate shadow offset based on rotation
  const offsetX = (rotateY / 20) * distance;
  const offsetY = (rotateX / -20) * distance;
  
  return `${offsetX}px ${offsetY}px ${blur}px ${color}`;
};

// Create 3D text shadow
export const generate3DTextShadow = (
  depth: number = 5,
  color: string = 'rgba(0,0,0,0.5)',
  direction: 'down' | 'up' | 'left' | 'right' = 'down'
): string => {
  // Generate shadow layers
  const shadows = [];
  const directionMap = {
    down: { x: 0, y: 1 },
    up: { x: 0, y: -1 },
    left: { x: -1, y: 0 },
    right: { x: 1, y: 0 }
  };
  
  const { x, y } = directionMap[direction];
  
  // Create multiple shadow layers for 3D effect
  for (let i = 1; i <= depth; i++) {
    shadows.push(`${x * i}px ${y * i}px 0px ${color}`);
  }
  
  return shadows.join(', ');
};

// Detect WebGL support
export const isWebGLSupported = (): boolean => {
  try {
    const canvas = document.createElement('canvas');
    return !!(
      window.WebGLRenderingContext && 
      (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
    );
  } catch (e) {
    return false;
  }
};

// Calculate performance level for adaptive effects
export const detectPerformanceLevel = (): 'high' | 'medium' | 'low' => {
  // Check for WebGL support as baseline
  if (!isWebGLSupported()) return 'low';
  
  // Check device memory (if available)
  if ('deviceMemory' in navigator) {
    const memory = (navigator as any).deviceMemory;
    if (memory && memory < 4) return 'low';
    if (memory && memory < 8) return 'medium';
  }
  
  // Check hardware concurrency (number of logical processors)
  if (navigator.hardwareConcurrency) {
    if (navigator.hardwareConcurrency < 4) return 'low';
    if (navigator.hardwareConcurrency < 8) return 'medium';
  }
  
  // Default to medium if we can't determine
  return 'medium';
};

// Create dynamic depth map for scene elements
export const createDepthMap = (elements: HTMLElement[], baseZ: number = 0): void => {
  elements.forEach((el, index) => {
    // Calculate depth based on position in document
    const rect = el.getBoundingClientRect();
    const depth = baseZ - (rect.top / window.innerHeight) * 100;
    
    // Apply transform with z-depth
    el.style.transform = `translateZ(${depth}px)`;
    el.style.zIndex = Math.floor(depth).toString();
  });
};

// Calculate parallax offset based on scroll position
export const calculateParallaxOffset = (
  scrollY: number, 
  element: HTMLElement,
  speed: number = 0.1
): number => {
  const rect = element.getBoundingClientRect();
  const centerY = rect.top + rect.height / 2;
  const viewportCenter = window.innerHeight / 2;
  
  // Calculate offset based on element position relative to viewport center
  const distanceFromCenter = centerY - viewportCenter;
  return distanceFromCenter * speed;
};

// Map a value from one range to another
export const mapRange = (
  value: number, 
  inMin: number, 
  inMax: number, 
  outMin: number, 
  outMax: number
): number => {
  return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin;
};
