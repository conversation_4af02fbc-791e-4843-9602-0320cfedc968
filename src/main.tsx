
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import LoadingAnimation from './components/LoadingAnimation'
import { StrictMode } from 'react'

// إضافة فئة التحميل إلى الجسم
document.body.classList.add('loading');

// إزالة أي عناصر mask-overlay موجودة مسبقًا
const existingMaskOverlay = document.getElementById('mask-overlay');
if (existingMaskOverlay && existingMaskOverlay.parentNode) {
  existingMaskOverlay.parentNode.removeChild(existingMaskOverlay);
}

// إنشاء عنصر قناع للتأثير الدائري
const maskOverlay = document.createElement('div');
maskOverlay.id = 'mask-overlay';
document.body.appendChild(maskOverlay);

// إزالة أي عناصر loading-root موجودة مسبقًا
const existingLoadingRoot = document.getElementById('loading-root');
if (existingLoadingRoot && existingLoadingRoot.parentNode) {
  existingLoadingRoot.parentNode.removeChild(existingLoadingRoot);
}

// إنشاء حاوية لرسوم التحميل المتحركة
const loadingRoot = document.createElement('div');
loadingRoot.id = 'loading-root';
loadingRoot.className = 'perspective-container';
document.body.appendChild(loadingRoot);

// عرض رسوم التحميل المتحركة
createRoot(loadingRoot).render(<LoadingAnimation />);

// عرض التطبيق الرئيسي مع StrictMode لاكتشاف المشاكل المحتملة
const rootElement = document.getElementById("root");
if (rootElement) {
  const root = createRoot(rootElement);
  root.render(
    <StrictMode>
      <App />
    </StrictMode>
  );
}

// إزالة رسوم التحميل المتحركة بعد تحميل التطبيق
window.addEventListener('load', () => {
  // إزالة أي عناصر ripple متبقية
  document.querySelectorAll('.loading-ripple, .loading-small-ripple').forEach(el => {
    if (el.parentNode) {
      el.parentNode.removeChild(el);
    }
  });
});
