

import HardwareHero from '@/components/hardware/HardwareHero';
import CircuitDiagrams from '@/components/hardware/CircuitDiagrams';
import CircuitComponents from '@/components/hardware/CircuitComponents';
import SupportedModelsCard from '@/components/themed/SupportedModelsCard';
import PricingCard from '@/components/themed/PricingCard';
import ThemedSection from '@/components/layout/ThemedSection';

const Hardware = () => {
  return (
    <div className="hardware-page">
      {/* Hardware Hero Section */}
      <HardwareHero />

      {/* Circuit Diagrams Section */}
      <CircuitDiagrams />

      {/* Circuit Components Section */}
      <CircuitComponents />

      {/* Supported Models Section */}
      <ThemedSection
        theme="hardware"
        id="hardware-supported-models"
        variant="primary"
        withParallax={true}
        animationVariant="fadeInUp"
      >
        <SupportedModelsCard theme="hardware" />
      </ThemedSection>

      {/* Pricing Section */}
      <ThemedSection
        theme="hardware"
        id="hardware-pricing"
        variant="secondary"
        withParallax={true}
        animationVariant="fadeInUp"
      >
        <PricingCard theme="hardware" />
      </ThemedSection>
    </div>
  );
};

export default Hardware;
