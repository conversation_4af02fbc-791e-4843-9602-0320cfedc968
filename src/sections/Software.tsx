
import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { toast } from "sonner";
import { ArrowRight, Download, CheckCircle2 } from "lucide-react";
import SectionHeader from '@/components/SectionHeader';
import AnimatedCard from '@/components/AnimatedCard';
import AnimatedCounter from '@/components/AnimatedCounter';
import SupportedModelsCard from '@/components/themed/SupportedModelsCard';
import PricingCard from '@/components/themed/PricingCard';
import ThemedSection from '@/components/layout/ThemedSection';
import ThemedHeader from '@/components/layout/ThemedHeader';
import EnhancedCard from '@/components/layout/EnhancedCard';

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3
    }
  }
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
};

const Software = () => {
  const { toast: toastNotify } = useToast();
  const [isVisible, setIsVisible] = useState(false);
  const [homeImageUrl, setHomeImageUrl] = useState("/lovable-uploads/46319556-27d1-46f3-b365-81927d12674f.png");
  const [latestUpdate, setLatestUpdate] = useState<{
    varizon: string;
    name: string | null;
    link: string | null;
  } | null>(null);
  const [stats, setStats] = useState({
    totalModels: 0,
    downloadCount: 0,
    distributorsCount: 0
  });

  useEffect(() => {
    setIsVisible(true);

    // Fetch home image from Supabase storage
    const fetchHomeImage = async () => {
      try {
        // Get home image from Supabase storage
        const {
          data: imageData
        } = await supabase.storage.from('website').getPublicUrl('Images/Home/home.png');

        if (imageData) {
          setHomeImageUrl(imageData.publicUrl);
        }
      } catch (error) {
        console.error('Error fetching home image:', error);
        toastNotify({
          title: "Image Loading Error",
          description: "Could not load the home image. Using fallback image instead.",
          variant: "destructive",
        });
      }
    };

    // Fetch latest software update
    const fetchLatestUpdate = async () => {
      try {
        const { data, error } = await supabase
          .from('update')
          .select('varizon, name, link')
          .order('release_at', { ascending: false })
          .limit(1);

        if (error) throw error;
        if (data && data.length > 0) {
          setLatestUpdate(data[0]);
        }
      } catch (error) {
        console.error('Error fetching latest update:', error);
      }
    };

    // Fetch statistics
    const fetchStats = async () => {
      try {
        // Get total models count
        const { data: modelSettings, error: modelError } = await supabase
          .from('settings')
          .select('numeric_value')
          .eq('key', 'total_models')
          .single();

        if (modelError) console.error('Error fetching total models:', modelError);

        // Get download count
        const { data: updateData, error: updateError } = await supabase
          .from('update')
          .select('download_count')
          .order('release_at', { ascending: false })
          .limit(1);

        if (updateError) console.error('Error fetching download count:', updateError);

        setStats({
          totalModels: modelSettings?.numeric_value || 0,
          downloadCount: updateData?.[0]?.download_count || 0,
          distributorsCount: stats.distributorsCount
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
      }
    };

    fetchHomeImage();
    fetchLatestUpdate();
    fetchStats();
  }, []);

  const handleDownload = async () => {
    try {
      if (latestUpdate?.link) {
        // Call the increment_counter function
        const { data, error: counterError } = await supabase.rpc('increment_counter');

        if (counterError) {
          console.error('Error incrementing download counter:', counterError);
          toast.error('Failed to process download request');
        } else {
          console.log('Download count increased to:', data);

          // Open the download link
          window.location.href = latestUpdate.link;
          toast.success('Download started!');
        }
      } else {
        toast.info("Download link is not available at the moment. Please try again later.");
      }
    } catch (error) {
      console.error('Error during download:', error);
      // Still provide download link even if counting fails
      if (latestUpdate?.link) {
        window.location.href = latestUpdate.link;
      }
    }
  };

  return (
    <div>
      {/* Hero Section */}
      <section className="pt-28 pb-20 bg-gradient-to-br from-orange-50/95 via-orange-100/85 to-orange-200/75 dark:from-gray-900/98 dark:via-gray-800/95 dark:to-orange-900/30 overflow-hidden relative">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <div className="absolute inset-0 bg-[url('/patterns/dots.svg')]"></div>
        </div>

        {/* Animated Gradient Overlay */}
        <div className="absolute inset-0 opacity-100 transition-opacity duration-1000 bg-gradient-to-br from-orange-400/8 via-orange-300/5 to-orange-600/12"></div>

        {/* Subtle Texture Overlay */}
        <div className="absolute inset-0 opacity-[0.02] bg-gradient-to-br from-orange-100 via-transparent to-orange-200 dark:from-gray-700 dark:via-transparent dark:to-gray-800"></div>
        {/* Animated background elements */}
        <motion.div
          className="absolute top-20 right-20 w-64 h-64 bg-pegasus-orange/5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.2, 0.3]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        ></motion.div>
        <motion.div
          className="absolute bottom-10 left-10 w-40 h-40 bg-pegasus-orange/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.2, 0.3, 0.2]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        ></motion.div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex flex-col md:flex-row items-center">
            <motion.div
              className="md:w-1/2 mb-10 md:mb-0"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7, ease: "easeOut" }}
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-gray-800 dark:text-white font-montserrat tracking-tight leading-tight">
                The Ultimate <span className="text-pegasus-orange bg-gradient-to-r from-pegasus-orange to-pegasus-orange-400 bg-clip-text text-transparent">Smartphone</span> Flashing Tool
              </h1>
              <motion.p
                className="text-lg md:text-xl text-gray-800 dark:text-gray-200 mb-8 max-w-lg"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.7 }}
              >
                Professional unlocking and flashing tool for smartphones, specializing in
                Xiaomi, Vivo, Oppo, Realme, Alcatel, Infinix, and more.
              </motion.p>
              <motion.div
                className="space-y-4 md:space-y-0 md:space-x-4 flex flex-col md:flex-row"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.7 }}
              >
                <Button
                  className="bg-pegasus-orange hover:bg-pegasus-orange-600 text-white px-6 py-6 rounded-full text-lg w-full md:w-auto transition-all duration-300 hover:-translate-y-1 shadow-lg hover:shadow-xl flex items-center justify-center group"
                  onClick={handleDownload}
                >
                  <Download className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                  <span className="relative">
                    Download Now {latestUpdate && `- ${latestUpdate.varizon}`}
                    <span className="absolute bottom-0 left-0 w-full h-0.5 bg-white/30 scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></span>
                  </span>
                </Button>
                <Button
                  className="bg-transparent border-2 border-pegasus-orange text-gray-800 dark:text-white px-6 py-6 rounded-full text-lg w-full md:w-auto transition-all duration-300 hover:-translate-y-1 shadow-lg hover:shadow-xl hover:bg-pegasus-orange hover:text-white flex items-center justify-center group"
                  onClick={() => document.getElementById('supported-models')?.scrollIntoView({ behavior: 'smooth' })}
                >
                  <ArrowRight className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                  <span className="relative">
                    Learn More
                    <span className="absolute bottom-0 left-0 w-full h-0.5 bg-current/30 scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></span>
                  </span>
                </Button>
              </motion.div>
            </motion.div>
            <motion.div
              className="md:w-1/2 relative z-10"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.7, ease: "easeOut" }}
            >
              <div className="relative flex items-center justify-center">
                <motion.div
                  className="bg-gradient-to-br from-orange-700/30 to-orange-900/20 rounded-full h-64 w-64 md:h-96 md:w-96 mx-auto absolute"
                  animate={{
                    scale: [1, 1.05, 1],
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                ></motion.div>
                <motion.div
                  className="absolute -left-10 top-1/4 w-24 h-24 bg-orange-500/10 rounded-full blur-xl"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.3, 0.5, 0.3]
                  }}
                  transition={{
                    duration: 6,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                ></motion.div>
                <motion.img
                  src={homeImageUrl}
                  alt="Pegasus Tool Interface"
                  className="relative z-10 max-w-full md:max-w-md mx-auto"
                  animate={{
                    y: [0, -10, 0],
                  }}
                  transition={{
                    duration: 6,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                  style={{
                    maxHeight: '500px',
                    objectFit: 'contain',
                    filter: 'drop-shadow(0 10px 15px rgb(0 0 0 / 0.5))'
                  }}
                  onError={(e) => {
                    // Fallback to local image if Supabase image fails
                    const target = e.target as HTMLImageElement;
                    target.src = "/lovable-uploads/46319556-27d1-46f3-b365-81927d12674f.png";
                  }}
                />
                <motion.div
                  className="absolute -right-10 top-1/4 w-20 h-20 bg-orange-400/10 rounded-full blur-xl"
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.3, 0.5, 0.3]
                  }}
                  transition={{
                    duration: 7,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                ></motion.div>
                <motion.div
                  className="absolute -left-5 bottom-1/4 w-32 h-32 bg-orange-300/10 rounded-full blur-xl"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.3, 0.6, 0.3]
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                ></motion.div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Bottom Accent Line */}
        <motion.div
          className="absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r from-transparent via-orange-400 to-transparent"
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 1.5, delay: 0.5 }}
          viewport={{ once: true }}
        />
      </section>

      {/* Features Section */}
      <ThemedSection
        theme="software"
        id="software-features"
        variant="secondary"
        withParallax={true}
        animationVariant="fadeInUp"
      >
        <ThemedHeader
          theme="software"
          title="Software Features"
          subtitle="Discover the powerful features of Pegasus Tool software"
          highlightWord="Features"
          size="normal"
        />

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16"
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.div variants={item}>
            <EnhancedCard
              theme="software"
              variant="glass"
              withGlow={true}
              delay={0.1}
              className="text-center"
            >
              <div className="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/20 rounded-full mb-6 shadow-md mx-auto">
                <CheckCircle2 className="h-8 w-8 text-pegasus-orange" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800 dark:text-white">User-Friendly Interface</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Intuitive design makes it easy to navigate and perform complex operations with just a few clicks.
              </p>
            </EnhancedCard>
          </motion.div>

          <motion.div variants={item}>
            <EnhancedCard
              theme="software"
              variant="glass"
              withGlow={true}
              delay={0.2}
              className="text-center"
            >
              <div className="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/20 rounded-full mb-6 shadow-md mx-auto">
                <CheckCircle2 className="h-8 w-8 text-pegasus-orange" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800 dark:text-white">Wide Device Compatibility</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Support for a vast range of smartphone models across multiple brands and operating systems.
              </p>
            </EnhancedCard>
          </motion.div>

          <motion.div variants={item}>
            <EnhancedCard
              theme="software"
              variant="glass"
              withGlow={true}
              delay={0.3}
              className="text-center"
            >
              <div className="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/20 rounded-full mb-6 shadow-md mx-auto">
                <CheckCircle2 className="h-8 w-8 text-pegasus-orange" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800 dark:text-white">Regular Updates</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Frequent software updates to add support for new models and improve existing features.
              </p>
            </EnhancedCard>
          </motion.div>
        </motion.div>
      </ThemedSection>

      {/* Supported Models Section */}
      <ThemedSection
        theme="software"
        id="software-supported-models"
        variant="primary"
        withParallax={true}
        animationVariant="fadeInUp"
      >
        <SupportedModelsCard theme="software" />
      </ThemedSection>

      {/* Pricing Section */}
      <ThemedSection
        theme="software"
        id="software-pricing"
        variant="secondary"
        withParallax={true}
        animationVariant="fadeInUp"
      >
        <PricingCard theme="software" />
      </ThemedSection>
    </div>
  );
};

export default Software;



