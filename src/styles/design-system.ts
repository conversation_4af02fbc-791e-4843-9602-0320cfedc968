// Global Design System for Visual Consistency
export type ThemeType = 'software' | 'hardware' | 'neutral';

export interface DesignTheme {
  // Background gradients
  primaryGradient: string;
  secondaryGradient: string;
  cardGradient: string;
  overlayGradient: string;

  // Colors
  primary: string;
  secondary: string;
  accent: string;
  text: string;

  // Borders and shadows
  border: string;
  shadow: string;
  hoverShadow: string;

  // Interactive states
  hover: string;
  focus: string;

  // Glass morphism effects
  glass: string;
  backdrop: string;

  // Animation colors
  glow: string;
  pulse: string;
}

export const designThemes: Record<ThemeType, DesignTheme> = {
  hardware: {
    primaryGradient: 'bg-gradient-to-br from-green-50/95 via-green-100/85 to-green-200/75 dark:from-gray-900/98 dark:via-gray-800/95 dark:to-green-900/30',
    secondaryGradient: 'bg-gradient-to-br from-green-100/90 via-green-50/95 to-green-200/80 dark:from-gray-800/95 dark:via-gray-900/90 dark:to-green-900/25',
    cardGradient: 'bg-gradient-to-br from-green-50/95 via-green-100/90 to-green-200/85 dark:from-gray-800/98 dark:via-gray-900/95 dark:to-green-900/40',
    overlayGradient: 'bg-gradient-to-br from-green-400/8 via-green-300/5 to-green-600/12',

    primary: 'text-green-600 dark:text-green-400',
    secondary: 'text-green-500 dark:text-green-300',
    accent: 'text-green-700 dark:text-green-500',
    text: 'text-gray-800 dark:text-gray-200',

    border: 'border-green-200/50 dark:border-green-700/40',
    shadow: 'shadow-xl shadow-green-100/40 dark:shadow-green-900/25',
    hoverShadow: 'hover:shadow-2xl hover:shadow-green-200/50 dark:hover:shadow-green-800/35',

    hover: 'hover:bg-green-50/90 dark:hover:bg-green-900/25',
    focus: 'focus:ring-green-500/40 focus:border-green-500/60',

    glass: 'bg-green-50/30 dark:bg-gray-800/30 backdrop-blur-2xl border-green-200/40 dark:border-green-700/35',
    backdrop: 'backdrop-blur-xl bg-green-50/40 dark:bg-gray-900/40',

    glow: 'shadow-[0_0_30px_rgba(34,197,94,0.25)]',
    pulse: 'animate-pulse text-green-400'
  },

  software: {
    primaryGradient: 'bg-gradient-to-br from-orange-50/95 via-orange-100/85 to-orange-200/75 dark:from-gray-900/98 dark:via-gray-800/95 dark:to-orange-900/30',
    secondaryGradient: 'bg-gradient-to-br from-orange-100/90 via-orange-50/95 to-orange-200/80 dark:from-gray-800/95 dark:via-gray-900/90 dark:to-orange-900/25',
    cardGradient: 'bg-gradient-to-br from-orange-50/95 via-orange-100/90 to-orange-200/85 dark:from-gray-800/98 dark:via-gray-900/95 dark:to-orange-900/40',
    overlayGradient: 'bg-gradient-to-br from-orange-400/8 via-orange-300/5 to-orange-600/12',

    primary: 'text-pegasus-orange',
    secondary: 'text-orange-500 dark:text-orange-300',
    accent: 'text-orange-700 dark:text-orange-500',
    text: 'text-gray-800 dark:text-gray-200',

    border: 'border-orange-200/50 dark:border-orange-700/40',
    shadow: 'shadow-xl shadow-orange-100/40 dark:shadow-orange-900/25',
    hoverShadow: 'hover:shadow-2xl hover:shadow-orange-200/50 dark:hover:shadow-orange-800/35',

    hover: 'hover:bg-orange-50/90 dark:hover:bg-orange-900/25',
    focus: 'focus:ring-orange-500/40 focus:border-orange-500/60',

    glass: 'bg-orange-50/30 dark:bg-gray-800/30 backdrop-blur-2xl border-orange-200/40 dark:border-orange-700/35',
    backdrop: 'backdrop-blur-xl bg-orange-50/40 dark:bg-gray-900/40',

    glow: 'shadow-[0_0_30px_rgba(251,146,60,0.25)]',
    pulse: 'animate-pulse text-orange-400'
  },

  neutral: {
    primaryGradient: 'bg-gradient-to-br from-gray-50/95 via-gray-100/85 to-gray-200/75 dark:from-gray-900/98 dark:via-gray-800/95 dark:to-gray-700/30',
    secondaryGradient: 'bg-gradient-to-br from-gray-100/90 via-gray-50/95 to-gray-200/80 dark:from-gray-800/95 dark:via-gray-900/90 dark:to-gray-700/25',
    cardGradient: 'bg-gradient-to-br from-gray-50/95 via-gray-100/90 to-gray-200/85 dark:from-gray-800/98 dark:via-gray-900/95 dark:to-gray-700/40',
    overlayGradient: 'bg-gradient-to-br from-gray-400/8 via-gray-300/5 to-gray-600/12',

    primary: 'text-gray-700 dark:text-gray-300',
    secondary: 'text-gray-600 dark:text-gray-400',
    accent: 'text-gray-800 dark:text-gray-200',
    text: 'text-gray-800 dark:text-gray-200',

    border: 'border-gray-200/50 dark:border-gray-600/40',
    shadow: 'shadow-xl shadow-gray-100/40 dark:shadow-gray-900/25',
    hoverShadow: 'hover:shadow-2xl hover:shadow-gray-200/50 dark:hover:shadow-gray-700/35',

    hover: 'hover:bg-gray-50/90 dark:hover:bg-gray-800/25',
    focus: 'focus:ring-gray-500/40 focus:border-gray-500/60',

    glass: 'bg-gray-50/30 dark:bg-gray-800/30 backdrop-blur-2xl border-gray-200/40 dark:border-gray-600/35',
    backdrop: 'backdrop-blur-xl bg-gray-50/40 dark:bg-gray-900/40',

    glow: 'shadow-[0_0_30px_rgba(107,114,128,0.25)]',
    pulse: 'animate-pulse text-gray-400'
  }
};

export const getTheme = (theme: ThemeType): DesignTheme => designThemes[theme];

// Animation variants for consistent motion design
export const motionVariants = {
  fadeInUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6, ease: "easeOut" }
  },

  fadeInLeft: {
    initial: { opacity: 0, x: -30 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.7, ease: "easeOut" }
  },

  fadeInRight: {
    initial: { opacity: 0, x: 30 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.7, ease: "easeOut" }
  },

  scaleIn: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: 0.5, ease: "easeOut" }
  },

  staggerContainer: {
    animate: {
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }
};

// Consistent spacing system
export const spacing = {
  section: 'py-20',
  sectionLarge: 'py-24',
  container: 'container mx-auto px-4',
  card: 'p-8',
  cardSmall: 'p-6',
  gap: 'gap-8',
  gapLarge: 'gap-12'
};
